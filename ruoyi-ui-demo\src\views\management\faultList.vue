<script>

import WarningList from "./warningList";

export default {
  name: "faultList",
  mixins: [WarningList],
  data() {
    return {
      pageTitle: "设备故障",

      queryParams:{
        pageNum: 1,
        pageSize: 10,
        buildingId: this.gf.getBuildingId(),
        deviceTypes: "",
        deviceType: "",
        severitys: "故障",  // 默认报警 一级，二级，三级
        hasFixed: 'N',
        flowStatus: null,
      },
    }
  },
  methods: {
  }
}
</script>