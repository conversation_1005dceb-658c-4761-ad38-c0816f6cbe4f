<template>
    <custCard :title="title" autoHeight :cardTitleShow="cardTitleShow">
      <div class="cont">
        <el-row>
          <el-col :span="24">
            <div class="title mb10">
                <div class="title_lt">
                  <span v-if="innerTitleShow">{{title}}</span>
                </div>
                <div class="txt_align_right">
                  <el-select clearable size="mini" :style="{'width': `${multiple ? 160 : 130}px`,'margin-right': '12px'}"
                    v-model="queryForm.selectedInd"
                    @change="updateSelected" :multiple="multiple" collapse-tags v-if="!deviceSelectHide">
                    <el-option
                      v-for="(dict,ind) in ids"
                      :key="dict.itemDataId"
                      :label="dict.name"
                      :value="ind"
                    />
                  </el-select>

                  <el-date-picker size="mini" style="width: 130px;"
                    v-if="!datePickerHide"
                    v-model="queryForm.date"
                    type="date"
                    placeholder="选择日期"
                    @change="handleUpdateData" />
                    <slot name="titleRight"/>
                </div>
            </div>


            <normal-chart
              class="chart"
              :height="`${getChartHeight}px`"
              :opts="deviceChartData" />

            <table :id="t.name" v-for="(t,i) in cacheData" style="display: none;">
              <tr>
                <td v-text="t.name"></td>
                <td>记录时间</td>
              </tr>
              <tr v-for="l in t.list">
                <td v-text="l.indication"></td>
                <td v-text="l.recordedAt"></td>
              </tr>
            </table>
          </el-col>
        </el-row>
      </div>
    </custCard>
</template>

<script type="text/javascript">
import {
  deviceItemDataList,
} from "@/api/device/apis";

import NormalChart from "@/views/components/NormalChart";
import custCard from "./screen/components/custCard.vue";
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    ids: {
      type: Array,
      default: () => [],
      note: "显示的曲线",
    },
    height: {
      type: Number,
      default: 100,
      note: "曲线高度",
    },
    multiple: {
      type: Boolean,
      default: false,
      note: "多图例,可多选"
    },
    allDeviceShow: {
      type: Boolean,
      default: false,
      note: "多图例时, multiple为true的前提下 可设置加载时显示全部"
    },
    deviceSelectHide: {
      type: Boolean,
      default: false,
      note: "隐藏设备选框"
    },
    datePickerHide: {
      type: Boolean,
      default: false,
      note: "隐藏日期组件"
    },
    innerTitleShow: {
      type: Boolean,
      default: false,
      note: "内部标题显示"
    },
    cardTitleShow: {
      type: Boolean,
      default: true,
      note: "卡片标题显示"
    },
    chartType: {
      type: String,
      default: 'line',
      note: "图表类型 chart/bar"
    },
    dataZoomHide: {
      type: Boolean,
      default: false,
      note: "图表是否可放大或者拖拽显示区域"
    },
    isTimer: {
      type: Boolean,
      default: false,
      note: "图表是否可放大或者拖拽显示区域"
    },
    timerStep: {
      type: Number,
      default: 15,
      note: "设置实时刷新时间间隔(秒)"
    },
    inTime: {
      type: Boolean,
      default: false,
    }
  },
  mixins: [],
  components: {
    custCard,
    NormalChart,
  },
  watch: {
    isTimer: {
      handler (val) {
        if(val){
          this.handleUpdateData();
          this.startTimeFresh()
        } else {
          clearInterval(this.timer);
          this.timer = null;
        }
      },
      immediate: true
    }
  },
  computed: {
    getChartHeight () {
      return this.height - 36
    }
  },
  data() {
    return {
      loading: false,
      curBuilding: this.gf.getCurBuilding(),

      //表单数据
      queryForm: {
        date: this.$moment().toDate(),
        // 选中的dataId
        deviceItemData: {},
        selectedInd: 0,
      },
      cacheData: [],
      deviceChartData: {},
      defaultRgbaColor: ['208, 222, 238'],
      timer: null,
      timerSplitLength: 10,
    }
  },
  created() {},
  mounted() {
    const firstItem = this.ids ? this.ids[0] : {};
    // 默认选中第一个

    if(this.multiple){
      if(this.allDeviceShow){
        this.queryForm.selectedInd = this.ids.map((item,idx) => idx);
        this.queryForm.deviceItemData = [...this.ids]
      } else {
        this.queryForm.selectedInd = [0];
        this.queryForm.deviceItemData = [firstItem]
      }

    } else {
      this.queryForm.deviceItemData = firstItem;
      this.queryForm.selectedInd = 0;
    }
    this.getData();
  },
  methods: {
    getData() {
      this.handleUpdateData();
    },
    updateSelected(ind) {
      console.log(ind,this.queryForm.selectedInd,'-----')
      this.queryForm.deviceItemData = [];
      if(this.multiple){
        this.queryForm.selectedInd = [...ind];
        ind.forEach(idx => {
          this.queryForm.deviceItemData.push(this.ids[idx])
        });
      } else {
        this.queryForm.deviceItemData = this.ids[ind];
        this.queryForm.selectedInd = ind;
      }
      this.handleUpdateData();
    },
    // 点击 [更新数据] 按钮
    handleUpdateData() {
      let limit = null;
      let resList = []
      if(!this.queryForm.dataRange && !this.inTime) {
        limit = 100;
      }
      if(this.queryForm.deviceItemData) {
        //0-24小时
        let xAxisData = Array.from({length: 25}, (v, k) => k < 10 ? `0${k}` : `${k}`)
        const xAxis = !this.inTime ? [
            {
              position: "bottom",
              data: xAxisData,
            }
          ] : [
            {
              type: 'time',
            }
          ]

        // 只在图表配置不存在或结构需要改变时重新初始化
        if (!this.deviceChartData || !this.deviceChartData.series || this.deviceChartData.series.length === 0) {
          this.deviceChartData = {
            animation: true,
            animationDuration: 300,
            animationEasing: 'cubicOut',
            legend: {
              data: [],
            },
            grid: {},
            xAxis: [...xAxis],
            yAxis: [],
            series: [],
          }
          this.deviceChartData.grid = {
            bottom: '0%',
            left: '2%',
            right: '2%',
            containLabel: true
          }
          if (!this.dataZoomHide) {
            this.deviceChartData.dataZoom = [
                {
                    type: 'inside'
                },
                // {
                //     type: 'slider',//显示当前图表显示的x轴区域
                // }
            ]
          }
        } else {
          // 只清空数据相关的部分，保留配置
          this.deviceChartData.legend.data = [];
          this.deviceChartData.yAxis = [];
          this.deviceChartData.series = [];
          this.deviceChartData.xAxis = [...xAxis];
        }

        if(!Array.isArray(this.queryForm.deviceItemData)) {
          resList = [this.queryForm.deviceItemData]
        } else {
          resList = [...this.queryForm.deviceItemData]
        }

        // 清空缓存数据，准备重新加载
        this.cacheData = [];
        resList.forEach(item => {
            this.getDeviceDataList(item, limit);
        })
      }
    },
    handleExportData() {
      this.cacheData.map((k,i) => {
        this.et(k.name, k.name);
        // this.ej(k.list, ["indication", "recordedAt"], k.name);
      });
    },

    // 设备数据详情
    getDeviceDataList(dt,limit) {
      const {deviceId, itemDataId, name, rgbColor, unit } = dt;
      const from = this.queryForm.date ? this.$moment(this.queryForm.date).startOf("day").format("YYYY-MM-DD HH:mm:ss") : null;
      const to = this.queryForm.date ? this.$moment(this.queryForm.date).endOf("day").format("YYYY-MM-DD HH:mm:ss") : null;
      const displayType = null;
      const agg = null;
      const displayName = name;
      const mainColor = rgbColor || this.defaultRgbaColor;
      this.loading = true;

      if(itemDataId && itemDataId != 'undefined'){
        deviceItemDataList({
          deviceId:deviceId,
          itemDataId: itemDataId,
          from:from,
          to:to,
          limit: limit,
          displayType: displayType,
          agg: agg,
        }).then( res => {
          console.log(res, res.data,'res.data');
          this.loading = false;
          if(!res.data) {
            this.deviceChartData = this.gf.getEmptyChartOpt();
            return false;
          }
          this.deviceChartData.title = {};
          this.deviceDataShow = true;
          var data = res.data;
          if(dt.data){
            //假数据显示
            data.data = dt.data.map(item => {
              return {
                indication: item.indication,
                recordedAt: this.$moment(this.queryForm.date).format("YYYY-MM-DD") + ' ' + item.recordedAt
              }
            })
          }
          let currentUnit = data.dataUnit || unit || '';
          // 补充两头的时间点
          data.data = data.data.concat([
            {
              recordedAt: from,
              indication: null,
            },
            {
              recordedAt: to,
              indication: null,
            }]);
          var seriesData = data.data.map(val => {
            return [this.$moment(val["recordedAt"]).valueOf(), val["indication"]];
          });
          seriesData = seriesData.sort((a, b) => {
            return a[0] > b[0];
          });
          data.name = displayName ? displayName : data.name; // 替换显示的名称
          this.deviceChartData.legend.data.push({
            name: data.name + currentUnit,
            // icon: 'roundRect',
            itemStyle: {
              color: `rgba(${mainColor}, 1)`
            }
          });
          //图例数大于1时显示图例
          this.deviceChartData.legend.show = this.deviceChartData.legend.data.length > 1;
          //如果已有同单位Y轴  不再push
          const hasSameUnit = (this.deviceChartData.yAxis || []).find(item => item.name === currentUnit);
          if(!hasSameUnit){
            this.deviceChartData.yAxis.push({
              type: 'value',
              name: currentUnit,
              position: 'left',
              alignTicks: true,
              offset: this.deviceChartData.yAxis.length * 30,
              boundaryGap: [0, '50%']
            })
          }
          //找到当前数据对应单位的y轴index
          const idx = (this.deviceChartData.yAxis || []).findIndex(item => item.name === currentUnit) || 0;
          this.deviceChartData.series.push({
            name: data.name + currentUnit,
            type: this.chartType,
            smooth: 0.6,
            xAxisIndex: 0,
            animationDuration: 300,
            animationEasing: 'cubicOut',
            animationDelay: 0,
            data: seriesData,
            symbol: 'none',
            lineStyle: {
              color: `rgba(${mainColor}, 1)`,
              width: 1
            },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: `rgba(${mainColor}, .5)`
                },
                {
                  offset: 1,
                  color: `rgba(${mainColor}, 0)`
                }
              ])
            },
            yAxisIndex: idx,
          });

          console.log(this.deviceChartData, 'this.deviceChartData');

          // 缓存数据
          this.cacheData.push({
            name: data.name,
            list: data.data,
          });
        });
      }
    },

    startTimeFresh () {
      this.timer = setInterval(this.handleUpdateData, this.timerStep * 1000)
    },
    //获取实时时间轴 传入时间间隔(秒) 坐标轴长度
    getHalvesBeforeNow(count, step) {
        const now = new Date();
        const halfs = [];
        for (let i = count - 1; i >= 0; i--) {
            const half = i * step * 1000;
            const dt = this.$moment(new Date(now - half)).format('HH:mm:ss')
            halfs.push(dt);
        }
        return halfs;
    },
  },
  beforeDestroy () {
    clearInterval(this.timer);
    this.timer = null;
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-input {
  width: 100%;
}
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
