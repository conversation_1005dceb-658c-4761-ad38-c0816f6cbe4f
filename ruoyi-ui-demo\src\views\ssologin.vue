<template>
    <div>sso login</div>
</template>

<script>
import store from '@/store'
import {
  getToken, setToken, removeToken,
  getPub<PERSON>ey, setPubKey, removePubKey, setPubKey2
} from '@/utils/auth';

export default {
  created() {
    // 加载配置
    this.getDicts("base_configs").then(response => {
      this.conf = response.data.filter(d => {
        if(d.dictLabel == "ssologin") {
          this.ssologin = d.dictValue;
        } else if(d.dictLabel == "ssoRedirect") {
          this.ssoRedirect = d.dictValue;
        } else if(d.dictLabel == "ssologinSuccess") {
          this.ssologinSuccess = d.dictValue;
        } else if(d.dictLabel == "ssologinFailed") {
          this.ssologinFailed = d.dictValue;
        }
      });
    });

    store.dispatch('getPublicKey')
      .then(res => {
        let publicKey = res.publicKey;
        setPubKey(publicKey);
        store.commit('SET_PUBLICKEY', publicKey);
        let publicKey2 = res.sPublicKey;
        setPubKey2(publicKey2);
        store.commit('SET_PUBLICKEY2', publicKey2);
      }).then(() => {
        // pass
      });
  },
  props: {},
  data() {
    return {
        token: this.$route.query.token,
        code: this.$route.query.code,
        tusn: this.$route.query.tusn,

        ssologin: null,
        ssoRedirect: null,
        ssologinSuccess: null,
        ssologinFailed: null,
    }
  },
  mounted() {
    // 尝试解析
    if(!this.code) {
      this.code = this.gf.request("code");
    }
    if(!this.token) {
      this.token = this.gf.request("token");
    }
    if(!this.tusn) {
      this.tusn = this.gf.request("tusn");
    }
    let that = this;
    setTimeout(() => {
      that.getData();
    }, 500);
  },
  methods: {
    getData() {
      console.log(this.token, this.code, this.tusn);
      debugger
      this.$store.dispatch("ssologin", { token: this.token, code: this.code, tusn: this.tusn })
        .then(() => {
         window.location.href = this.ssologinSuccess;
        }).catch((e) => {
         window.location.href = this.ssologinFailed;
      });
    }
  }
}
</script>
