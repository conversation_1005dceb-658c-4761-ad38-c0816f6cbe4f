<template>
    <div class="warning-card">
        <el-card :class="['warning-box-card', warningClass]">
            <div class="card-header flex">
                <div class="flex-1 flex">
                    <span class="card-title mr10">{{ data.id }}</span>
                    <i :class="['card-badge', {'has-fixed': data.hasFixed === 'Y'}]">{{ data.hasFixedText }}</i>
                    <i :class="['card-badge', {'confirm': data.flowStatus === 'confirm'}, {'ignore': data.flowStatus === 'ignore'}]">{{ data.flowStatusText }}</i>
                </div>
                <el-checkbox :label="data.id"></el-checkbox>
            </div>
            <div  class="card-body flex-1">
                <ul class="flex waring-card-tabs">
                    <li>
                        <h4>{{ data.standardValue }}</h4>
                        <p>标准值</p>
                    </li>
                    <li>
                        <h4>{{ data.actualValue }}</h4>
                        <p>实际值</p>
                    </li>
                    <li>
                        <h4 class="severity">{{ data.severity }}</h4>
                        <p>报警级别</p>
                    </li>
                </ul>
                <div class="waring-card-summary flex">
                    <div class="waring-icon"></div>
                    <div class="flex-1">
                        <p>{{ data.errMsg }}</p>
                        <p v-if="data.solutionRef">{{ data.solutionRef }}</p>
                        <p v-if="data.note">{{ data.note }}</p>
                    </div>
                </div>
                <ul class="waring-card-info">
                    <li class="flex">
                        <span>设备名称：</span>
                        <p>{{ data.deviceName }}（{{ data.deviceId }}）</p>
                    </li>
                    <li class="flex">
                        <span>发生时间：</span>
                        <p>{{ data.reportedAt || data.createdAt }}</p>
                    </li>
                    <li class="flex">
                        <span>结束时间：</span>
                        <p>{{ data.finishedAt || data.updatedAt }}</p>
                    </li>
                    <li class="flex">
                        <span>发生地点：</span>
                        <p>{{ data.deviceLocation }} - {{ data.devicePosition }}</p>
                    </li>
                </ul>
            </div>
            <div class="card-bottom">
                <el-button type="primarry" size="mini" plain @click="handleViewVideo">视频</el-button>
                <el-button type="success" size="mini" plain @click="handleUpdate"
                    v-hasRole="['admin','operator']"
                    >操作</el-button>
                <el-button type="warning" size="mini" plain @click="handleRepair">报修</el-button>
                <el-button type="danger" size="mini" plain @click="handleDelete"
                  v-hasRole="['admin','operator']">删除</el-button>
            </div>
        </el-card>
    </div>
</template>
<script>
import { deleteDeviceWarning } from "@/api/device/apis";
export default {
    name: 'WarningCard',
    props: {
        data: {
            type: Object,
            require: true,
            default: () => []
        }
    },
    data() {
        return {
            warningSeverity: {
                '一级': 'warning-severity-1',
                '二级': 'warning-severity-2',
                '三级': 'warning-severity-3',
            },

            warningFixed: {
                'Y': 'warning-severity-4',
                'N': '',
            }
        }
    },
    computed: {
        warningClass() {
            if(this.data.hasFixed == "Y") {
                return "warning-severity-4";
            } else {
                return this.warningSeverity[this.data.severity];
            }
        },
    },
    methods: {
        
        // 设备报修
        handleRepair() {
            // this.$emit("repair", this.data);
            const {deviceId = '', id = '' } = this.data;
            this.$router.push({
                path:'/maintananceMgt/repair',
                query:{
                    deviceId,
                    warningId: id,
                    method:"add"
                }
            });
        },

        // 删除设备
        handleDelete() {
            const id = this.data.id;
            this.$emit("delete", id);
        },

        // 查看视频
        handleViewVideo() {
            window.open('https://vd3.bdstatic.com/mda-jf3pk3mguuvd5ccs/sc/mda-jf3pk3mguuvd5ccs.mp4')
        },

        handleUpdate() {
            this.$emit("handleUpdate", this.data);
        }
    }
}
</script>
<style lang="scss" scoped>
.warning-card {
    margin-left: 20px;
    margin-bottom: 20px;
}
.warning-box-card {
    flex-direction: column;
    width: 310px;
    height: 100%;
    background: #0A192F;
    border: 1px solid #275681;
    border-radius: 4px;
    ::v-deep .el-card__body {
        display: flex;
        flex-direction: column;
        padding: 16px 16px 24px;
        height: 100%;
    }
    .el-button--mini {
        padding-top: 5px;
        padding-bottom: 4px;
    }
    .card-header {
        align-items: center;
        .flex {
            align-items: center;
        }
        .card-badge {
            display: inline-block;
            height: 16px;
            line-height: 16px;
            margin-right: 10px;
            font-size: 12px;
            color: #fff;
            background-color: rgba(108, 128, 151, 0.5);
            padding: 0 6px;
            border-radius: 50px;
            &.has-fixed,
            &.confirm {
                background-color: rgba(13, 211, 163, 0.3);
            }
            &.ignore {
                background-color: rgba(34, 148, 254, 0.3);
            }
        }
        
        .card-title {
            font-size: 16px;
            font-weight: bold;
        }
        ::v-deep .el-checkbox__label {
            display: none;
        }
    }
    .card-body {
        position: relative;
        margin: 16px 0;
        padding: 16px;
        border-radius: 2px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        background-color: rgba(255, 255, 255, 0.08);
        overflow: hidden;
        .waring-card-tabs {
            li {
                position: relative;
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                text-align: center;
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    right: 0;
                    width: 1px;
                    height: 14px;
                    margin-top: -7px;
                    background: rgba(255, 255, 255, 0.3);
                }
                &:last-child::after {
                    display: none;
                }
                h4 {
                    margin-bottom: 3px;
                    font-size: 16px;
                    color: #fff;
                }
                p {
                    font-size: 12px; 
                    color: rgba(255, 255, 255, 0.6);
                }
            }
        }
        .waring-card-summary {
            position: relative;
            z-index: 10;
            align-items: center;
            margin-top: 10px;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            .waring-icon {
                width: 22px;
                height: 20px;
                background: url('/images/warning-record/grade_icon_4_1.png') no-repeat center center;
                background-size: 100% 100%;
            }
            p {
                padding-left: 10px;
                font-size: 14px;
            }
        }
        .waring-card-info {
            position: relative;
            z-index: 10;
            font-size: 12px;
            li {
                margin-bottom: 3px;
            }
            span {
                white-space: nowrap;
                color: rgba(255, 255, 255, 0.6);
            }
        }
        &::after {
            content: url('/images/warning-record/grade_icon_4_2.png');
            position: absolute;
            right: -5px;
            bottom: -6px;
        }
    }
    .card-bottom {
        text-align: right;
    }
}


.warning-severity-4 {
    .card-header .card-badge {
        background-color: rgba(108, 128, 151, 0.5);
    }
    .card-body {
        border: 1px solid rgba(255, 255, 255, 0.3);
        background-color: rgba(255, 255, 255, 0.08);
        .waring-card-tabs li {
            &::after {
                background: rgba(255, 255, 255, 0.3);
            }
            .severity {
                color: #ddd;
            }
        }
        .waring-card-summary {
            background: rgba(220, 220, 220, 0.2);
            .waring-icon {
                background: url('/images/warning-record/grade_icon_1_1.png') no-repeat center center;
                background-size: 100% 100%;
            }
            p {
                color: rgba(255, 255, 255, 0.6);
            }
        }
        &::after {
            content: url('/images/warning-record/grade_icon_1_2.png');
        }
    }
    
}
.warning-severity-3 {
    .card-header .card-badge {
        background-color: rgba(252, 216, 105, 0.7);
    }
    .card-body {
        border: 1px solid rgba(252, 216, 105, 0.5);
        background: rgba(252, 216, 105, 0.08);
        .waring-card-tabs li {
            &::after {
                background: #FCD869;
            }
            .severity {
                color: #FCD869;
            }
        }
        .waring-card-summary {
            background: rgba(252, 216, 105, 0.2);
            .waring-icon {
                background: url('/images/warning-record/grade_icon_1_1.png') no-repeat center center;
                background-size: 100% 100%;
            }
            p {
                color: #FCD869;
            }
        }
        &::after {
            content: url('/images/warning-record/grade_icon_1_2.png');
        }
    }
    
}
.warning-severity-2 {
    .card-header .card-badge {
        background-color: #D78737;
    }
    .card-body {
        border: 1px solid rgba(215, 135, 55, 0.5);
        background: rgba(215, 135, 55, 0.15);
        .waring-card-tabs li {
            &::after {
                background: #D78737;
            }
            .severity {
                color: #D78737;
            }
        }
        .waring-card-summary {
            background: rgba(215, 135, 55, 0.2);
            .waring-icon {
                background: url('/images/warning-record/grade_icon_2_1.png') no-repeat center center;
                background-size: 100% 100%;
            }
            p {
                color: #D78737;
            }
        }
        &::after {
            content: url('/images/warning-record/grade_icon_2_2.png');
        }
    }
    
}
.warning-severity-1 {
    .card-header .card-badge {
        background-color: #CA334E;
    }
    .card-body {
        border: 1px solid rgba(202, 51, 78, 0.5);
        background: rgba(202, 51, 78, 0.08);
        .waring-card-tabs li {
            &::after {
                background: rgba(202, 51, 78, 0.3);
            }
            .severity {
                color: #CA334E;
            }
        }
        .waring-card-summary {
            background: rgba(202, 51, 78, 0.2);
            .waring-icon {
                background: url('/images/warning-record/grade_icon_3_1.png') no-repeat center center;
                background-size: 100% 100%;
            }
            p {
                color: #CA334E;
            }
        }
        &::after {
            content: url('/images/warning-record/grade_icon_3_2.png');
        }
    }
    
}
</style>