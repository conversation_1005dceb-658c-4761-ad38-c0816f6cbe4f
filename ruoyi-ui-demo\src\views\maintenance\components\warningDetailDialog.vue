<template>
    <div>
        <el-dialog
        class="assetsDetail"
        :modal="false"
        width="650px"
        :visible.sync="show"
        v-drag="true" >
        <div slot="title" style="cursor: move;">
            报警信息详情
        </div>
        <div class="box-card">
            <el-form label-width="120px" :model="detail">
            <el-form-item label="报警编号" prop="id">
                <div v-text="detail.id"></div>
            </el-form-item>
            <el-form-item label="标准值" prop="compareStandard">
                <div v-text="detail.compareStandard"></div>
            </el-form-item>
            <el-form-item label="实际值" prop="compareCurrent">
                <div v-text="detail.compareCurrent"></div>
            </el-form-item>
            <el-form-item label="报警级别" prop="severity">
                <div v-text="detail.severity"></div>
            </el-form-item>
            <el-form-item label="报警信息" prop="errMsg">
                <div v-text="detail.errMsg"></div>
            </el-form-item>
            <el-form-item label="解决办法" prop="solutionRef">
                <div v-text="detail.solutionRef"></div>
            </el-form-item>
            <el-form-item label="设备名称" prop="deviceName">
                <div v-text="detail.deviceName"></div>
            </el-form-item>
            <el-form-item label="发生时间" prop="createdAt">
                <div v-text="detail.createdAt"></div>
            </el-form-item>
            <el-form-item label="结束时间" prop="updatedAt">
                <div v-text="detail.updatedAt"></div>
            </el-form-item>
            <el-form-item label="发生地点" prop="deviceLocation">
                <div v-text="detail.deviceLocation"></div>
            </el-form-item>
            </el-form>
        </div>
        </el-dialog>
    </div>
</template>
<script>
import { deviceWarningDetail } from "@/api/device/apis";

export default {
    name: '',
   
    data() {
        return {
            show: false,
            detail: {},
        }
    },
    computed: {
        
    },
    methods: {
        // 更新显示弹窗
        handleUpdate (id, deviceId) {
            this.show = true;
            this.getDetail(id,deviceId)
        },
        getDetail (id = '', deviceId = '') {
            const params = {
                itemWarningId: id,
                deviceId
            }
            deviceWarningDetail(params).then(res => {
                console.log(res)
                const dt = res.data
                const compare = JSON.parse(dt.compare) || [];
                dt.compareStandard = compare[0]
                dt.compareCurrent = compare[2]
                this.detail = dt
            })
        }
    }
    
}
</script>
