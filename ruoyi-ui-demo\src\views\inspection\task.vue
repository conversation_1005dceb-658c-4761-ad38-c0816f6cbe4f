<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="计划" prop="planId">
        <el-select v-model="queryParams.planId" placeholder="请选择计划" clearable size="small">
          <el-option
            v-for="dict in planIdOptions"
            :key="dict.id"
            :label="dict.name"
            :value="parseInt(dict.id)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计划执行人" prop="executor">
        <el-select v-model="queryParams.executor" placeholder="请选择计划执行人" clearable size="small">
          <el-option
              v-for="dict in userList"
              :key="dict.userId"
              :label="dict.nickName"
              :value="dict.userId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:task:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:task:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:task:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:task:export']"
        >导出</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" fixed="left" width="55" align="center" />
      <el-table-column label="计划" align="center" prop="planId" :formatter="planIdFormat" />
      <el-table-column label="计划执行人" align="center" prop="executor" :formatter="executorFormat" />
      <el-table-column label="实际执行人" align="center" prop="executorReal" :formatter="executorRealFormat" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="status" :formatter="statusFormat" />
      <el-table-column label="执行结果状态" align="center" prop="statusResult" :formatter="statusResultFormat" />
      <el-table-column label="备注信息" align="center" prop="note" />
      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:task:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:task:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改巡检任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="计划" prop="planId">
          <el-select v-model="form.planId" placeholder="请选择计划">
            <el-option
              v-for="dict in planIdOptions"
              :key="dict.id"
              :label="dict.name"
              :value="parseInt(dict.id)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计划执行人" prop="executor">
          <el-select v-model="form.executor" placeholder="请选择计划执行人">
            <el-option
                v-for="dict in userList"
                :key="dict.userId"
                :label="dict.nickName"
                :value="dict.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实际执行人" prop="executorReal">
          <el-select v-model="form.executorReal" placeholder="请选择实际执行人">
            <el-option
                v-for="dict in userList"
                :key="dict.userId"
                :label="dict.nickName"
                :value="dict.userId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.startTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.endTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任务状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择任务状态">
            <el-option
              v-for="dict in statusOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="parseInt(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行结果状态" prop="statusResult">
          <el-select v-model="form.statusResult" placeholder="请选择执行结果状态">
            <el-option
              v-for="dict in statusResultOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="parseInt(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注信息" prop="note">
          <el-input v-model="form.note" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask, exportTask } from "@/api/inspection/task";
import { listPlan  } from "@/api/inspection/plan";
import { listUser } from "@/api/system/user";
export default {
  name: "Task",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 巡检任务表格数据
      taskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 计划Id字典
      planIdOptions: [],
      // 计划执行人字典
      executorOptions: [],
      // 实际执行人字典
      executorRealOptions: [],
      // 任务状态. 0:未开始;1:执行中;2:已完成;3:已忽略字典
      statusOptions: [],
      // 执行结果状态. 0:正常;1:异常;字典
      statusResultOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planId: null,
        executor: null,
        executorReal: null,
        startTime: null,
        endTime: null,
        status: null,
        statusResult: null,
        note: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      userList: []
    };
  },
  async created() {
    await this.getPreData();
    this.getList();

  },
  methods: {
    /** 查询巡检任务列表 */
    getList() {
      this.loading = true;
      listTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 计划Id字典翻译
    planIdFormat(row, column) {
      return (this.planIdOptions.find(item => item.id === row.planId) || {}).name || ''
    },
    // 计划执行人字典翻译
    executorFormat(row, column) {
      return (this.userList.find(item => item.userId === row.executor) || {}).nickName || ''
    },
    // 实际执行人字典翻译
    executorRealFormat(row, column) {
      return (this.userList.find(item => item.userId === row.executorReal) || {}).nickName || ''
    },
    // 任务状态. 0:未开始;1:执行中;2:已完成;3:已忽略字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 执行结果状态. 0:正常;1:异常;字典翻译
    statusResultFormat(row, column) {
      return this.selectDictLabel(this.statusResultOptions, row.statusResult);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        planId: null,
        executor: null,
        executorReal: null,
        startTime: null,
        endTime: null,
        status: null,
        statusResult: null,
        note: null,
        createBy: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加巡检任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTask(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改巡检任务";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTask(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTask(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除巡检任务编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delTask(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有巡检任务数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportTask(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    planLit () {
      listPlan().then(response => {
        this.planIdOptions = response.rows;
      });
    },
    // 所有用户
    getUserList () {
      listUser({pageNum:1, pageSize:1000000}).then(response => {
        this.userList = response.rows;
      });
    },
    getPreData () {
      this.getUserList();
      this.planLit();
      this.getDicts("inspection_task_status").then(response => {
        this.statusOptions = response.data;
      });
      this.getDicts("inspection_checked_status").then(response => {
        this.statusResultOptions = response.data;
      });
    }
  }
};
</script>
<style scoped lang="scss">
.el-select {
    width: 100%;
}
</style>