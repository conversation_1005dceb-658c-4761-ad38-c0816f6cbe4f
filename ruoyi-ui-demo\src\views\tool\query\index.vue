<template>
  <div class="device-summary">
    <h3>
      <span v-text="pageTitle ? pageTitle : '查询测试'"></span>
      <el-divider></el-divider>
    </h3>

    <el-form class="mt20" ref="basicInfoForm" :model="query" :rules="rules" label-width="150px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="查询语句" prop="sql">
            <el-input placeholder="请输入内容" type="textarea" :rows="10" v-model="query.sql" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="" prop="">
            <el-button type="primary" @click="handleEncrypt">编码</el-button>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button type="primary" @click="handleDecrypt">解码</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <p class="mb10"> 编/解码结果: </p>
    <el-input
      type="textarea"
      :rows="4"
      placeholder="编码结果"
      v-model="sqlEncode">
    </el-input>

    <el-divider></el-divider>
    <p class="mb10"> 查询结果: 合计 {{ total }}</p>
    <el-table :data="tableData" v-if="tableData && tableData.length > 0" stripe height='600'>
      <el-table-column v-for="(col, index) in Object.keys(tableData[0])"
                       :key="index"
                       :prop="col"
                       :label="col">
      </el-table-column>
    </el-table>
    <div class="mb20" />

  </div>
</template>

<script>
import { doSqlWithPage, decodeSql } from "@/api/base/apis";
import { encrypt, decrypt } from '@/utils/jsencrypt';
import {
  getPubKey2
} from '@/utils/auth';
export default {
  name: "toolQuery",
  mixins: [ ], // 继承父模块
  components: {
  },
  data() {
    return {
      pageTitle: "查询测试",

      publicKey: getPubKey2(),

      query: {
        sql: null,
      },
      rules: {
        sql: [
          { required: true, message: "请输入内容", trigger: "blur" }
        ],
      },

      total: 0,
      tableData: [],

      sqlEncode: "",
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleQuery() {
      if(this.query.sql) {
        doSqlWithPage({
          sql: JSON.stringify(this.gf.encrypt(this.query.sql, this.publicKey)),
          pageNum: 1,
          pageSize: 10,
        }).then(res => {
          this.total = res.total;
          this.tableData = res.rows;
        });
      }
    },

    handleEncrypt() {
      this.sqlEncode = JSON.stringify(this.gf.encrypt(this.query.sql, this.publicKey));
    },

    handleDecrypt() {
      decodeSql({
        sql: this.query.sql,
      }).then(res => {
        this.sqlEncode = res.data;
      });
    }
  }
}
</script>
