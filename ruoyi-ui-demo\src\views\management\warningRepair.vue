<template>
  <!-- 添加或修改设备报修对话框 -->
  <el-dialog width="500px"
    :title="title"
    :visible.sync="open" 
    :before-close="cancel"
    class="common-dialog">
    <el-tabs tab-position="top" v-model="type">
        <el-tab-pane name="0" label="内部报修">
            <el-form ref="form" class="dialog-form" :model="form" :rules="rules" label-width="96px" v-if="form.id > 0">
                <el-form-item label="设备编号" prop="deviceId">
                    <el-input v-model="form.deviceId" disabled placeholder="设备编号" />
                </el-form-item>
                <el-form-item label="设备位置" prop="position">
                    <el-input v-model="form.position" disabled placeholder="设备位置" />
                </el-form-item>
                <el-form-item label="故障时间" prop="createTime">
                    <el-date-picker v-model="form.createTime" type="datetime" placeholder="选择故障时间"></el-date-picker>
                </el-form-item>
                <el-form-item label="问题描述" prop="record">
                    <el-input type="textarea" v-model="form.record" placeholder="问题描述" />
                </el-form-item>
                <el-form-item label="图片或视频" prop="pictures">
                    <el-upload
                    :action="uploadFileUrl"
                    :limit="limitImg"
                    :on-error="handleUploadError"
                    :on-exceed="handleExceed"
                    :on-remove="handleDeleteUrl"
                    :on-success="handleUploadSuccess"
                    :on-preview="handlePreview"
                    :headers="headers"
                    :file-list="pictureList"
                    :class="{'hide-upload-btn': hideUploadImg}"
                    >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <span slot="tip" class="el-upload__tip ml20">最多上传9个</span>
                    </el-upload>
                </el-form-item>
                <el-form-item label="工单责任人" prop="operator">
                    <el-select v-model="form.operator" clearable placeholder="请选择" size="small">
                    <el-option
                        v-for="dict in userList"
                        :key="dict.userId+''"
                        :label="dict.nickName"
                        :value="dict.userId+''"
                        >
                        <span style="float: left">{{ dict.nickName }}</span>
                        <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.phonenumber }}</span>
                    </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitForm('form')">确 定</el-button>
                  <el-button @click="cancel">取 消</el-button>
                </el-form-item>
            </el-form>
        </el-tab-pane>
        <el-tab-pane name="1" label="对外发布">
            <el-form ref="formForeign" class="dialog-form" :model="form" :rules="rules" label-width="96px" v-if="form.id > 0">
                <el-form-item label="设备编号" prop="deviceId">
                    <el-input v-model="form.deviceId" disabled placeholder="设备编号" />
                </el-form-item>
                <el-form-item label="设备位置" prop="position">
                    <el-input v-model="form.position" disabled placeholder="设备位置" />
                </el-form-item>
                <el-form-item label="故障时间" prop="createTime">
                    <el-date-picker v-model="form.createTime" type="datetime" placeholder="选择故障时间"></el-date-picker>
                </el-form-item>
                <el-form-item label="问题描述" prop="record">
                    <el-input type="textarea" v-model="form.record" placeholder="问题描述" />
                </el-form-item>
                <el-form-item label="图片或视频" prop="pictures">
                    <el-upload
                    multiple
                    accept="image/jpeg,image/png,video/mp4"
                    :action="uploadFileUrl"
                    :limit="limitImg"
                    :on-error="handleUploadError"
                    :on-exceed="handleExceed"
                    :on-remove="handleDeleteUrl"
                    :on-success="handleUploadSuccess"
                    :on-preview="handlePreview"
                    :headers="headers"
                    :file-list="pictureList"
                    :class="{'hide-upload-btn': hideUploadImg}"
                    >
                    <el-button size="small" type="primary">点击上传</el-button>
                    <span slot="tip" class="el-upload__tip ml20">最多上传9个</span>
                    </el-upload>
                </el-form-item>
                <el-form-item label="工单责任人" prop="operator">
                    <el-input v-model="form.operator" placeholder="工单责任人" />
                </el-form-item>
                <el-form-item label="联系电话" prop="mobile">
                    <el-input v-model="form.mobile" placeholder="联系电话" />
                </el-form-item>
                </el-form-item>
                <el-form-item label="指定平台" prop="plantform">
                    <el-select v-model="form.plantform" clearable placeholder="请选择" size="small">
                    <el-option
                        v-for="dict in plantformList"
                        :key="dict.id+''"
                        :label="dict.name"
                        :value="dict.id+''"
                        >
                        <span style="float: left">{{ dict.name }}</span>
                    </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitForm('formForeign')">确 定</el-button>
                  <el-button @click="cancel">取 消</el-button>
                </el-form-item>
            </el-form>
        </el-tab-pane>
    </el-tabs>
    
  </el-dialog>
</template>

<script>
import { addMaintenance } from "@/api/device/maintenance";
import { listUser } from "@/api/system/user";
import {getToken} from "@/utils/auth";

export default {
  name: "WarningRepair",
  components: {},
  data() {
    return {
      open: false,
      title: "设备报修",
      type: 0,

      userList: [],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      limitImg: 9,
      limitVideo: 9,
      pictureList: [],
      // 表单参数
      deviceInfo: {},
      form: {},
      // 表单校验
      rules: {
        createTime: [{ required: true, message: '请选择故障时间', trigger: 'change' }],
        record: [{ required: true, message: '请填写问题描述', trigger: 'blur' }],
        operator: [{ required: true, message: '请选择责任人', trigger: 'change' }],
      },

      plantformList: [
        {
          id: 1,
          "name": "58同城",
        },
        {
          id: 2,
          "name": "百姓网",
        },
        {
          id: 3,
          "name": "美团",
        }
      ],
    }
  },
  computed: {
    hideUploadImg() {
        return this.pictureList.length >= this.limitImg
    },
    errowMsg() {

    },
  },
  created() {
    
    // 所有用户
    listUser().then(response => {
      this.userList = response.rows;
    });
  },
  methods: {
    /** 弹框关闭 */
    cancel() {
      this.open = false;
    },
    
    /** 报修按钮操作 */
    handleRepair(row) {
      const {id, deviceId, deviceLocation, devicePosition, createdAt, errMsg, solutionRef } = JSON.parse(JSON.stringify(row));
      this.form = {
        id,
        deviceId,
        createTime: createdAt,
        record:  `${ errMsg || '' }\n${ solutionRef || '' }`,
        position: `${ deviceLocation || '' } - ${ devicePosition || '' }`,
        operator: '',
        plantform: "",
      }
      this.open = true;
    },

    
    // 删除文件
    handleDeleteUrl(file,fileList) {
      this.pictureList = fileList;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`最多上传9项内容`);
    },
    // 上传失败
    handleUploadError(err) {
      // console.log(err)
      this.$message.error("上传失败, 请重试");
    },
    // 文件预览
    handlePreview(file) {
      window.open(file.url)
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      // console.log(res)
      if(res.code == 500){
        this.$message.error("上传失败, 请重试");
        return
      }

      this.$message.success("上传成功");
      const fileType = this.checkFileType(file.name);
      this.pictureList.push({
        type: fileType,
        name: file.name,
        url: res.url
      });
    },

    /** 提交按钮 */
    submitForm(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            if(this.pictureList.length>0){
                this.form.pictures = JSON.stringify(this.pictureList);
            }
            const params = {
              ...this.form,
              type: '紧急维修',
              status: 'new'
            }
            console.log(params)
            // return
            addMaintenance(params).then(response => {
                this.msgSuccess("提交成功");
                this.open = false;
                this.$emit("updateData");
                }).catch(e => {
                // pass
                });
          }
        });
    },

    // 判断文件类型
    checkFileType(fileValue) {
      const index = fileValue.lastIndexOf("."); 
      const fileValueSuffix = fileValue.substring(index).toLowerCase(); 
          if (/(.*)\.(mp4|avi|wmv|mpg|mpeg|mov|rm|ram|swf|flv)$/.test(fileValueSuffix)) { // 判断是否符合视频格式
            return 'video';
          } else if(/(.*)\.(jpg|jpeg|png|bmp|gif)$/.test(fileValueSuffix)) { //判断是否符合图片格式
            return 'image';
          } else if (/(.*)\.(doc|docx|xls|xlsx|ppt|pptx|pdf|txt)$/.test(fileValueSuffix)) { // 判断是否符合文档格式
            return 'document';
          } else if(/(.*)\.(rar|zip|gz|bz2|)$/.test(fileValueSuffix)) { //判断是否符合压缩文件格式
            return 'image';
          }
      return 'unknown';
  }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .dialog-form {
    // .el-upload--picture-card,
    // .el-upload-list__item {
    //     width: 96px;
    //     height: 96px;
    //     line-height: 96px;
    //     margin: 0 6px 6px 0;
    // }
    .hide-upload-btn .el-upload--picture-card {
        display: none;
    }
}
</style>
