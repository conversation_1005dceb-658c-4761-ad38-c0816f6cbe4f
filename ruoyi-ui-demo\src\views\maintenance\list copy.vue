<template>
  <div class="app-container">
    <div class="main-card p15">
      <h3 v-text="pageTitle"></h3><br>

      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="110px">
        <el-form-item label="设备" prop="deviceId">
          <el-select v-model="queryParams.deviceId" placeholder="请选择设备" size="small">
            <el-option
              v-for="dict in deviceList"
              :key="dict.id"
              :label="dict.name"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="操作员" prop="operator">
          <el-input
            v-model="queryParams.operator"
            placeholder="请输入操作员"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="mobile" class="pr15">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入联系电话"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['device:maintenance:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="el-icon-edit"
            size="mini"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['device:maintenance:edit']"
            v-hasRole="['admin','operator']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['device:maintenance:remove']"
            v-hasRole="['admin','operator']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="el-icon-download"
            size="mini"
            plain
            @click="handleExport"
            v-hasPermi="['device:maintenance:export']"
          >导出</el-button>
        </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" stripe :data="maintenanceList" >
        <el-table-column label="#" align="center" fixed="left" width="50" prop="id" />
        <el-table-column label="设备" align="center" fixed="left" prop="deviceId" :formatter="deviceIdFormat" />
        <el-table-column label="维保分类" align="center" prop="type" />
        <el-table-column label="执行状态" align="center" prop="status"  >
          <template slot-scope="scope">
            <span v-if="scope.row.status==null">未处理</span>
            <span v-if="scope.row.status!=null">{{ scope.row.status=='new'?'未处理': (scope.row.status=='doing'?'处理中':'已完成')}}</span>
          </template>
        </el-table-column>
        <el-table-column label="执行记录" align="center" prop="record" />
        <el-table-column label="相关图片" align="center" prop="pictures">
          <template slot-scope="scope">
            <div class="item" v-if="scope.row.pictureList.length > 0">
              <div class="demo-image__preview">
                <el-image
                  v-for="(pic, index) in scope.row.pictureList"
                  style="width: 50px; height: 50px; margin:0 10px 10px 0; border:1px solid #ddd;"
                  :src="pic"
                  fit="scale-down"
                  :preview-src-list="scope.row.pictureList" :key="index">
                </el-image>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注信息" align="center" prop="note" />
        <el-table-column
          prop="operator"
          label="操作人"
          width="120">
          <template slot-scope="scope">
            <div>
              <div v-text="userFormat(scope.row.operator)"></div>
              <div v-text="scope.row.mobile"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdAt" width="100">
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}<br>
              {{ parseTime(scope.row.createdAt, '{h}:{i}:{s}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updatedAt" width="100">
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}<br>
              {{ parseTime(scope.row.updatedAt, '{h}:{i}:{s}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              plain
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['device:maintenance:edit']"
              v-hasRole="['admin','operator']"
            >修改</el-button>
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        :background="false"
      />
      <!-- 添加或修改设备维保记录对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="设备ID" prop="deviceId">
            <el-input v-model="form.deviceId" placeholder="请输入设备ID" />
          </el-form-item>
          <el-form-item label="维保分类" prop="type" >
            <el-select v-model="form.type" placeholder="请选择维保分类" >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行记录" prop="record">
            <el-input v-model="form.record" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="执行记录图片" prop="pictures">
<!--            <el-input v-model="form.pictures" type="textarea" placeholder="请输入内容" />-->
            <el-upload
              :action="uploadFileUrl"
              :limit="9"
              :on-error="handleUploadError"
              :on-exceed="handleExceed"
              :on-remove="handleDeleteUrl"
              :on-success="handleUploadSuccess"
              :headers="headers"
              :file-list="form.pictureList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              最多上传9张图片
            </el-upload>
          </el-form-item>
          <el-form-item label="备注信息" prop="note">
            <el-input v-model="form.note" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="操作员" prop="operator">
            <el-input v-model="form.operator" placeholder="请输入操作员" />
          </el-form-item>
          <el-form-item label="执行状态">
            <el-select v-model="form.status" placeholder="请选择执行状态" clearable size="small">
              <el-option label="未处理" value="new" />
              <el-option label="处理中" value="doing" />
              <el-option label="已完成" value="finished" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入联系电话" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      </div>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";
import { deviceFullList } from "@/api/device/apis";
import { listMaintenance, getMaintenance, delMaintenance, addMaintenance, updateMaintenance, exportMaintenance } from "@/api/device/maintenance";
import {getToken} from "@/utils/auth";

export default {
  name: "maintenanceList",
  data() {
    return {
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 遮罩层
      loading: true,
      single: true,
      multiple: true,
      // 建筑ID
      buildingId: this.gf.getBuildingId(),
      pageTitle: "报修记录",
      type: "",
      showSearch: false,
      open: false,
      title: "",
      // 总条数
      total: 0,
      maintenanceList: [],
      typeOptions:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        deviceId: null,
        operator: null,
        mobile: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      dataList: [],
      deviceList:[],

      // 是否已修复字典
      hasFixedOptions: [],
      // 操作状态字典
      flowStatusOptions: [],
      // 设备种类
      deviceTypeOptions: [],
      // 用户列表
      userList: [],
    };
  },
  created() {
    let r = this.$route.path.split("/").pop();
    // 报修记录 type 是 紧急维修
    // 保养记录 type 是 日常维护，预见性维修，计划性维修，设备改进性维修
    // 巡检记录 type 是 日常巡检
    // 保养计划 type 是 保养计划
    if(r == "repair" ){
      this.pageTitle = "报修记录";
      this.queryParams.type = "紧急维修"
      this.typeOptions.push({value: "紧急维修", label: "紧急维修",})
    } else if(r == "maintainance") {
      this.pageTitle = "保养记录";
      this.queryParams.type = "日常维护,预见性维修,计划性维修,设备改进性维修"
      this.typeOptions.push({value: "日常维护", label: "日常维护",})
      this.typeOptions.push({value: "预见性维修", label: "预见性维修",})
      this.typeOptions.push({value: "计划性维修", label: "计划性维修",})
      this.typeOptions.push({value: "设备改进性维修", label: "设备改进性维修",})
    } else if( r == "inspection") {
      this.pageTitle = "巡检记录";
      this.queryParams.type = "日常巡检"
      this.typeOptions.push({value: "日常巡检", label: "日常巡检",})
    } else if(r == "maintainancePlan") {
      this.pageTitle = "保养计划";
      this.queryParams.type = "保养计划"
      this.typeOptions.push({value: "保养计划", label: "保养计划",})
    }
    this.getList();
    this.getDicts("d_device_warning_status").then(response => {
      this.hasFixedOptions = response.data;
    });
    this.getDicts("a_item_warning").then(response => {
      this.flowStatusOptions = response.data;
    });
    this.getDicts("d_device_type").then(response => {
      this.deviceTypeOptions = response.data;
    });
    // 所有设备
    deviceFullList({buildingId:this.buildingId}).then(response => {
      this.deviceList = response.data;
    });
    // 所有用户
    listUser({pageNum:1, pageSize:1000000}).then(response => {
      this.userList = response.rows;
    });
  },
  mounted() {
  },
  methods: {
    // 删除文件
    handleDeleteUrl(file,fileList) {
      this.form.pictureList = fileList;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`最多上传9张图片`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$message.error("上传失败, 请重试");
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if(res.code==500){
        this.$message.error("上传失败, 请重试");
        return
      }
      this.$message.success("上传成功");
      console.info(res);
      // file.name
      this.form.pictureList.push({
        name:file.name,
        url: res.fileName
      });
    },
    /** 查询设备维保记录列表 */
    getList() {
      this.loading = true;
      listMaintenance(this.queryParams).then(response => {
        this.maintenanceList = response.rows.map( d=> {
          d.pictureList = d.pictures ? d.pictures.split(",") : [];
          return d;
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        type: null,
        record: null,
        pictures: null,
        pictureList:[],
        note: null,
        operator: null,
        status: "new",
        mobile: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMaintenance(id).then(response => {
        this.form = response.data;
        if(this.form.pictures!=null){
          this.form.pictureList = JSON.parse(this.form.pictures);
        }else{
          this.form.pictureList = [];
        }
        this.open = true;
        this.title = "修改记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log(this.form)
          debugger
          if(this.form.pictureList.length>0){
            this.form.pictures = JSON.stringify(this.form.pictureList);
          }
          if (this.form.id != null) {
            updateMaintenance(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaintenance(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除设备维保记录编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delMaintenance(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有设备维保记录数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportMaintenance(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    // 设备
    deviceIdFormat(row, column) {
      var opts = this.deviceList.map(b => {
        return {
          dictValue: b.id,
          dictLabel: b.name,
        }
      });
      return this.selectDictLabel(opts, row.deviceId);
    },
    // 设备有效字典翻译
    hasFixedFormat(row, column) {
      return this.selectDictLabel(this.hasFixedOptions, row.hasFixed);
    },
    // 设备有效字典翻译
    flowStatusFormat(row, column) {
      return this.selectDictLabel(this.flowStatusOptions, row.operatorType);
    },
    // 用户Id翻译
    userFormat(operator) {
      let u = this.userList.filter( d => {
        return d.userId == operator;
      }).pop();
      return u ? u.nickName : '';
    },

  }
}
</script>

<style type="text/css">
.app-container { padding: 0 20px; }
</style>

