<template>
  <div class="app-container new-system-container repair-record-wrap">
    <div class="main-card">
      <h3 v-text="pageTitle"></h3>
      <el-divider></el-divider>
      <!-- 功能区 -->
      <el-row>
        <el-col :md="8">
          <div class="repair-record-ribbon-left">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['device:maintenance:add']"
            >新增</el-button>
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              plain
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['device:maintenance:edit']"
              v-hasRole="['admin','operator']"
            >修改</el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              plain
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
            >删除</el-button>
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              plain
              @click="handleExport"
              v-hasPermi="['device:maintenance:export']"
            >导出</el-button>
          </div>
        </el-col>
        <el-col :md="16" class="repair-record-ribbon-right">
          <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="0">
            <el-form-item prop="deviceId">
              <el-select v-model="queryParams.deviceId" placeholder="请选择设备" size="mini">
                <el-option
                  v-for="dict in deviceList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="operator">
              <el-input
                v-model="queryParams.operator"
                placeholder="请输入操作员"
                clearable
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item prop="mobile">
              <el-input
                v-model="queryParams.mobile"
                placeholder="请输入联系电话"
                clearable
                size="mini"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item class="mr0">
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <!-- <el-button type="success" icon="el-icon-refresh" plain size="mini" @click="getList" >刷新</el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- 列表 -->
      <el-table v-loading="loading" stripe :data="maintenanceList" >
        <el-table-column label="#" align="center" width="80" prop="id" />
        <el-table-column label="设备" align="center" prop="deviceId" :formatter="deviceIdFormat" />
        <el-table-column label="维保分类" align="center" prop="type" width="90"/>
        <el-table-column label="执行状态" align="center" prop="status"  width="90">
          <template slot-scope="scope">
            <span v-if="scope.row.status==null || scope.row.status=='new'">待派单</span>
            <span v-if="scope.row.status == 'dispatched'">已派单</span>
            <span v-if="scope.row.status == 'received'">已接单</span>
            <span v-if="scope.row.status == 'doing'">处理中</span>
            <span v-if="scope.row.status == 'finished'">已完成</span>
          </template>
        </el-table-column>
        <el-table-column label="执行记录" align="center" prop="note" />
        <el-table-column label="报警信息" align="center" prop="record" />
        
        <el-table-column label="备注信息" align="center" prop="note" />
        <el-table-column
          prop="operator"
          label="操作人"
          width="120">
          <template slot-scope="scope">
            <div>
              <div v-text="userFormat(scope.row.operator)"></div>
              <div v-text="scope.row.mobile"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdAt" width="100">
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}<br>
              {{ parseTime(scope.row.createdAt, '{h}:{i}:{s}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="finishedAt" width="100">
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.finishedAt, '{y}-{m}-{d}') }}<br>
              {{ parseTime(scope.row.finishedAt, '{h}:{i}:{s}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updatedAt" width="100">
          <template slot-scope="scope">
            <span>
              {{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}<br>
              {{ parseTime(scope.row.updatedAt, '{h}:{i}:{s}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="320" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              plain
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['device:maintenance:edit']"
              v-hasRole="['admin','operator']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              plain
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['device:maintenance:edit']"
              v-hasRole="['admin','operator']"
            >修改</el-button>
            <!-- <el-button
              size="mini"
              type="text"
              plain
              @click="handleViewFiles(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
            >查看文件</el-button> -->
            <el-button
              size="mini"
              type="text"
              plain
              @click="handleViewReportDetail(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
              v-if="scope.row.repairOrderId"
            >查看报修单详情</el-button>
            <el-button
              size="mini"
              type="text"
              plain
              @click="handleWarningDetail(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
              v-if="scope.row.warningId"
            >查看报警详情</el-button>
            <el-button
              size="mini"
              type="text"
              plain
              @click="handleDeviceDetail(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
              v-if="scope.row.deviceId"
            >查看设备详情</el-button>
            <!-- <el-button
              size="mini"
              type="text"
              plain
              @click="handleViewImg(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
            >查看图片</el-button> -->
            <el-button
              size="mini"
              type="danger"
              plain
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['device:maintenance:remove']"
              v-hasRole="['admin','operator']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        :background="false"
      />
      <!-- 添加或修改设备维保记录对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body class="common-dialog" :before-close="handleClose">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <!-- <el-form-item label="设备ID" prop="deviceId">
            <el-input v-model="form.deviceId" placeholder="请输入设备ID" />
          </el-form-item> -->
          <el-form-item label="设备" prop="deviceId">
            <el-select v-model="form.deviceId" clearable filterable placeholder="请选择设备" size="small">
              <el-option
                v-for="dict in deviceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
                >
                <span style="float: left">{{ dict.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.position }}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="维保分类" prop="type" >
            <el-select v-model="form.type" placeholder="请选择维保分类" >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行记录" prop="record">
            <el-input v-model="form.record" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <el-form-item label="执行记录图片" prop="pictures">
<!--            <el-input v-model="form.pictures" type="textarea" placeholder="请输入内容" />-->
            <el-upload
              :action="uploadFileUrl"
              :limit="9"
              :on-error="handleUploadError"
              :on-exceed="handleExceed"
              :on-remove="handleDeleteUrl"
              :on-success="handleUploadSuccess"
              :headers="headers"
              :file-list="form.pictureList"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <span slot="tip" class="el-upload__tip ml20">最多上传9个</span>
              <el-tooltip class="item" effect="dark" placement="top-start">
                <div slot="content">
                    图片: bmp, gif, jpg, jpeg, png,<br />
                    视频: mp4, avi, wmv, mpg, mpeg, mov, rm, ram, swf, flv,<br />
                    文档: doc, docx, xls, xlsx, ppt, pptx, pdf, txt,<br />
                    压缩文件: rar, zip, gz, bz2,<br />
                    文件大小不超过 50M
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-upload>
          </el-form-item>
          <el-form-item label="备注信息" prop="note">
            <el-input v-model="form.note" type="textarea" placeholder="请输入内容" />
          </el-form-item>
          <!-- <el-form-item label="操作员" prop="operator">
            <el-input v-model="form.operator" placeholder="请输入操作员" />
          </el-form-item> -->
          <el-form-item label="操作员" prop="operator">
            <el-select v-model="form.operator" clearable placeholder="请选择" size="small">
              <el-option
                v-for="dict in userList"
                :key="dict.userId+''"
                :label="dict.nickName"
                :value="dict.userId+''"
                >
                <span style="float: left">{{ dict.nickName }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.phonenumber }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执行状态">
            <el-select v-model="form.status" placeholder="请选择执行状态" clearable size="small">
              <el-option :label="item.title" :value="item.status" v-for="(item,index) in statusMap" :key="index"/>
            </el-select>
          </el-form-item>
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入联系电话" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </el-dialog>
      </div>

      <!-- 图片视频列表弹窗 -->
      <ViewFilesDialog ref="viewFilesDialog"/>

      <!-- 当前工单关联的保修单或报警单的详情 -->
      <RepairOrderDetailDialog ref="repairOrderDetailDialogRef"></RepairOrderDetailDialog>
      <WarningDetailDialog ref="warningDetailDialogRef"></WarningDetailDialog>
      <DeviceInfoDialog ref="baseDialogRef"></DeviceInfoDialog>
      <Detail ref="detailRef" :device-list="deviceList"></Detail>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";
import { deviceFullList } from "@/api/device/apis";
import { listMaintenance, getMaintenance, delMaintenance, addMaintenance, updateMaintenance, exportMaintenance } from "@/api/device/maintenance";
import { getToken } from "@/utils/auth";
import ViewFilesDialog from './viewFilesDialog'
import RepairOrderDetailDialog from './components/repairOrderDetailDialog'
import WarningDetailDialog from './components/warningDetailDialog'
import DeviceInfoDialog from "./components/deviceInfoDialog"
import Detail from "./detail";
import { statusMap } from './common/index'

export default {
  name: "maintenanceList",
  components: {
    ViewFilesDialog,
    RepairOrderDetailDialog,
    WarningDetailDialog,
    DeviceInfoDialog,
    Detail
  },
  data() {
    return {
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // 遮罩层
      loading: true,
      single: true,
      multiple: true,
      // 建筑ID
      buildingId: this.gf.getBuildingId(),
      pageTitle: "报修记录",
      type: "",
      open: false,
      title: "",
      // 总条数
      total: 0,
      maintenanceList: [],
      typeOptions:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        deviceId: null,
        operator: null,
        mobile: null,
        // 默认按日期倒序
        orderByColumn: "updatedAt",
        isAsc: "desc",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      dataList: [],
      deviceList:[],

      // 是否已修复字典
      hasFixedOptions: [],
      // 操作状态字典
      flowStatusOptions: [],
      // 设备种类
      deviceTypeOptions: [],
      // 用户列表
      userList: [],
      deviceData: {},
      statusMap
    };
  },
  created() {
    let r = this.$route.path.split("/").pop();
    // 运维记录 type 是 紧急维修
    // 保养记录 type 是 日常维护，预见性维修，计划性维修，设备改进性维修
    // 巡检记录 type 是 日常巡检
    // 保养计划 type 是 保养计划
    if(r == "repair" ){
      this.pageTitle = "报修记录";
      this.queryParams.type = "紧急维修"
      this.typeOptions.push({value: "紧急维修", label: "紧急维修",})
    } else if(r == "maintainance") {
      this.pageTitle = "保养记录";
      this.queryParams.type = "日常维护,预见性维修,计划性维修,设备改进性维修"
      this.typeOptions.push({value: "日常维护", label: "日常维护",})
      this.typeOptions.push({value: "预见性维修", label: "预见性维修",})
      this.typeOptions.push({value: "计划性维修", label: "计划性维修",})
      this.typeOptions.push({value: "设备改进性维修", label: "设备改进性维修",})
    } else if( r == "inspection") {
      this.pageTitle = "巡检记录";
      this.queryParams.type = "日常巡检"
      this.typeOptions.push({value: "日常巡检", label: "日常巡检",})
    } else if(r == "maintainancePlan") {
      this.pageTitle = "保养计划";
      this.queryParams.type = "保养计划"
      this.typeOptions.push({value: "保养计划", label: "保养计划",})
    }
    this.getList();
    this.getDicts("d_device_warning_status").then(response => {
      this.hasFixedOptions = response.data;
    });
    this.getDicts("a_item_warning").then(response => {
      this.flowStatusOptions = response.data;
    });
    this.getDicts("d_device_type").then(response => {
      this.deviceTypeOptions = response.data;
    });
    // 所有设备
    deviceFullList({buildingId:this.buildingId}).then(response => {
      this.deviceList = response.data;
    });
    // 所有用户
    listUser({pageNum:1, pageSize:1000000}).then(response => {
      this.userList = response.rows;
    });
  },
  mounted() {
    let method = this.$route.query.method;
    let deviceId = this.$route.query.deviceId;
    let deviceType = this.$route.query.deviceType;
    if(method == "add") {
      this.reset();
      this.form.deviceId = parseInt(deviceId);
      this.form.type = this.typeOptions[0].value;
      this.open = true;
      this.title = "添加维修记录";
    }
  },
  methods: {
    // 删除文件
    handleDeleteUrl(file,fileList) {
      this.form.pictureList = fileList;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`最多上传9张图片`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$message.error("上传失败, 请重试");
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if(res.code==500){
        this.$message.error("上传失败, 请重试");
        return
      }
      this.$message.success("上传成功");
      // console.info(res);
      // file.name
      this.form.pictureList.push({
        name:file.name,
        url: res.url,
      });
    },
    /** 查询设备维保记录列表 */
    getList() {
      this.loading = true;
      listMaintenance(this.queryParams).then(response => {
        this.maintenanceList = response.rows.map( d=> {
          try {
            d.pictureList = JSON.parse(d.pictures);
          } catch(e) {
            d.pictureList = [];
          }
          return d;
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceId: null,
        type: null,
        record: null,
        pictures: null,
        pictureList:[],
        note: null,
        operator: null,
        status: "new",
        mobile: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMaintenance(id).then(response => {
        this.form = response.data;
        this.form.pictureList = [];
        if(this.form.pictures!=null){
          try {
            this.form.pictureList = JSON.parse(this.form.pictures);
          } catch(e) {
            // pass
          }
        }
        this.open = true;
        this.title = "修改记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log(this.form)
          if(this.form.pictureList.length>0){
            this.form.pictures = JSON.stringify(this.form.pictureList);
          }
          // 移除更新时间，数据库自动更新
          delete this.form.updatedAt;
          const query = this.$route.query;
          const {repairOrderId = '', warningId = ''} = query;
          if(repairOrderId){
            this.form.repairOrderId = repairOrderId
          }
          if(warningId){
            this.form.warningId = warningId
          }

          if (this.form.id != null) {
            updateMaintenance(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaintenance(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.deleteParamsNoNeed()
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除设备维保记录编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delMaintenance(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },

    // 查看文件
    handleViewFiles(row) {
      this.$refs.viewFilesDialog.handleUpdate(row.pictureList)
    },

    // // 查看图片
    // handleViewImg(row) {
    //   this.$refs.viewFilesDialog.handleUpdate({type: 'image', data: row.pictureList})
    // },

    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有设备维保记录数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportMaintenance(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },
    // 设备
    deviceIdFormat(row, column) {
      var opts = this.deviceList.map(b => {
        return {
          dictValue: b.id,
          dictLabel: b.name,
        }
      });
      return this.selectDictLabel(opts, row.deviceId);
    },
    // 设备有效字典翻译
    hasFixedFormat(row, column) {
      return this.selectDictLabel(this.hasFixedOptions, row.hasFixed);
    },
    // 设备有效字典翻译
    flowStatusFormat(row, column) {
      return this.selectDictLabel(this.flowStatusOptions, row.operatorType);
    },
    // 用户Id翻译
    userFormat(operator) {
      let u = this.userList.filter( d => {
        return d.userId == operator;
      }).pop();
      return u ? u.nickName : '';
    },
    deleteParamsNoNeed () {
      const newQuery = JSON.parse(JSON.stringify(this.$route.query));
      for(let i in newQuery) {
        delete newQuery[i]
      }
      this.$router.replace({
        query: newQuery
      })
    },
    handleClose () {
      if(!this.form.id){
        this.deleteParamsNoNeed()
      }
      this.cancel();
    },
    handleViewReportDetail (data) {
     const {repairOrderId = '', deviceId = ''} = data
      let id = repairOrderId
      this.$refs.repairOrderDetailDialogRef.handleUpdate(id, deviceId)
    },
    handleWarningDetail (data) {
     const {warningId = '', deviceId = ''} = data
      let id = warningId
      this.$refs.warningDetailDialogRef.handleUpdate(id, deviceId)
    },
    handleDeviceDetail (data) {
     const {deviceId = '', resourceId = ''} = data
     this.$refs.baseDialogRef.handleUpdate(resourceId, deviceId)
    },
    handleView (data) {
      const {id = ''} = data
      this.$refs.detailRef.handleUpdate(id)
    }
  }
}
</script>

<style lang="scss" scoped>
.repair-record-wrap { 
  .repair-record-ribbon-left {
    // padding-top: 5px;
    // text-align: right;
    button {
      margin-top: 5px;
    }
  }
  .repair-record-ribbon-right {
    text-align: right;
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

</style>


