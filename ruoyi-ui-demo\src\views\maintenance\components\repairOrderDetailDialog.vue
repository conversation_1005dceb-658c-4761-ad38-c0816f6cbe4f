<template>
  <div>
    <el-dialog
      class="assetsDetail"
      :modal="false"
      width="650px"
      :visible.sync="show"
      v-drag="true"
    >
      <div slot="title" style="cursor: move">报修单详情</div>
      <div class="box-card">
        <Steps :status="detail.status"></Steps>
        <el-form label-width="120px" :model="detail">
          <el-form-item label="报修单编号" prop="orderId">
            <div v-text="detail.orderId"></div>
          </el-form-item>
          <el-form-item label="报修类型" prop="repairTypeName">
            <div v-text="detail.repairTypeName"></div>
          </el-form-item>
          <el-form-item label="设备" prop="deviceId">
            <div v-text="detail.deviceId"></div>
          </el-form-item>
          <el-form-item label="报修状态" prop="status">
            <div v-text="detail.statusName"></div>
          </el-form-item>
          <el-form-item label="问题描述" prop="content">
            <div v-text="detail.content"></div>
          </el-form-item>
          <el-form-item label="楼栋" prop="building">
            <div v-text="detail.building"></div>
          </el-form-item>
          <el-form-item label="楼层" prop="floorLevel">
            <div v-text="detail.floorLevel"></div>
          </el-form-item>
          <el-form-item label="故障地址" prop="address">
            <div v-text="detail.address"></div>
          </el-form-item>
          <el-form-item label="报修人" prop="applyBy">
            <div v-text="detail.applyBy"></div>
          </el-form-item>
          <el-form-item label="联系电话" prop="updatedAt">
            <div v-text="detail.mobile"></div>
          </el-form-item>
          <el-form-item label="报修人公司" prop="company">
            <div v-text="detail.company"></div>
          </el-form-item>
          <el-form-item label="创建时间" prop="createdAt">
            <div v-text="detail.createdAt"></div>
          </el-form-item>
          <el-form-item label="更新时间" prop="updatedAt">
            <div v-text="detail.updatedAt"></div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { repairOrderDetail } from "@/api/device/apis";
import Steps from './steps.vue'
import { getStatusName } from '../common/index'
export default {
  name: "viewFilesDialog",

  data() {
    return {
      show: false,
      detail: {},
      repairTypeMap: [
        {
          value: 'electrical',
          text: "电气维修"
        },{
          value: 'waterPipes',
          text: "水管维修"
        },{
          value: 'metope',
          text: "墙面维修"
        },{
          value: 'other',
          text: "其他维修"
        },
      ]
    };
  },
  computed: {},
  components: {
    Steps
  },
  methods: {
    // 更新显示弹窗
    handleUpdate(id) {
      this.show = true;
      this.getDetail(id);
    },
    getDetail(id = "") {
      const params = {
        id,
      };
      repairOrderDetail(params).then((res) => {
        this.detail = res.data;
        const statusName = getStatusName(this.detail.status);
        this.detail.statusName = statusName;
        this.detail.repairTypeName = (this.repairTypeMap.find(item => item.value === this.detail.repairType) || {}).text;
      });
    },
  },
};
</script>
<style lang="scss" scoped>

</style>
