<template>
  <div class="app-container new-system-container">
    <div class="main-card">
      <h3>
        <span v-text="this.pageTitle"></span>

        <span class="pull-right">
          <el-select class="mr20" v-model="refreshSpan" clearable placeholder="定时刷新" size="small">
            <el-option
              v-for="item in refreshSpanOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </span>


        <el-divider></el-divider>
      </h3>

      <el-tabs class="deviceTypes"
        tab-position="top"
        v-model="deviceType"
        @tab-click="handleDeviceTypeChange">
        <el-tab-pane :name="t.type" v-for="(t,index) in deviceTypesDisplay" :key="index">
          <span slot="label" class="tabTitle">
              {{ deviceTypeName(t.type) }}
              <span class="tab-badge" :class=" t.warningNum > 0 ? 'tab-badge-active' : '' ">{{ t.warningNum }}</span>
          </span>
          <!-- 功能区 -->
          <div class="ribbon-wrap">
            <span class="pull-left">
              <span>查询:</span>
              <el-select v-model="queryParams.hasFixed" size="mini" clearable placeholder="报警状态" @change="handleSelectChange">
                <el-option
                  v-for="item in hasFixedOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue">
                </el-option>
              </el-select>
              <el-select v-model="queryParams.flowStatus" size="mini" clearable placeholder="操作状态" @change="handleSelectChange">
                <el-option
                  v-for="item in flowStatusOptions"
                  :key="item.dictValue"
                  :label="item.dictLabel"
                  :value="item.dictValue">
                </el-option>
              </el-select>
              <el-select v-model="orderBy" size="mini" clearable placeholder="排序方式" @change="handleSelectChange">
                <el-option
                  v-for="item in sortOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </span>

            <span class="pull-right">
              <span>操作:</span>
              <el-checkbox
                :indeterminate="isIndeterminate" v-model="checkedAll"
                :disabled="!checkedIdArray.length" @change="checkAllChange"
                class="ribbon-checkbox mr10">
                {{ checkedAll ? '取消全选': '一键全选' }}
              </el-checkbox>

              <el-dropdown class="mr10"
                @command="handleConfirmChecked"
                :disabled="!checkList.length"
                v-hasRole="['admin','operator']">
                <el-button type="success" plain icon="el-icon-edit" size="mini">
                  操作 <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="item.dictValue" v-text="item.dictLabel" v-for="item in flowStatusOptions" />
                </el-dropdown-menu>
              </el-dropdown>

              <el-button type="danger" plain icon="el-icon-delete" size="mini" class="mr20"
                @click="handleDeleteChecked"
                :disabled="!checkList.length"
                v-hasRole="['admin','operator']">删除</el-button>

              <el-button type="warning" plain icon="el-icon-download" size="mini"
                @click="handleExport"
              >导出</el-button>
            </span>
            <div class="clearfix" />
          </div>
          <el-divider></el-divider>
          <!-- 列表 -->
          <div v-loading="loading">
            <el-checkbox-group class="flex warning-record-container" v-model="checkList" @change="handleCheckedChange">
              <template v-for="item in warningList">
                <WarningCard :data="item" :key="item.id"
                  @delete="handleDelete"
                  @repair="handleRepair"
                  @handleUpdate="handleUpdate" />
              </template>
            </el-checkbox-group>
          </div>

        </el-tab-pane>
      </el-tabs>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    </div>

    <WarningRepair
        ref="warningRepairDialog"
        @updateData="updateData" />
    <DeviceWarningDialog
        ref="deviceWarningDialog"
        @updateData="updateData" />
  </div>
</template>

<script>
import { listUser, getUserProfile } from "@/api/system/user";
import {
  deviceSummary,
  deviceFullList,
} from "@/api/device/apis";
import {
  deviceWarningList,
  deviceWarningListExport,
  deleteDeviceWarning,
  updateDeviceWarning,
} from "@/api/device/apis";

import DeviceWarningDialog from "@/views/device/monitor/components/Device/components/dialog/components/deviceWarningDialog.vue";
import WarningCard from './warningCard'
import WarningRepair from "./warningRepair";

export default {
  name: "warningList",
  data() {
    return {
      // 遮罩层
      loading: true,
      curBuilding: this.gf.getCurBuilding(),
      settings: this.gf.projectSettings(),
      user: {},

      pageTitle: "设备报警",

      queryParams:{
        pageNum: 1,
        pageSize: 10,
        buildingId: this.gf.getBuildingId(),
        deviceTypes: "",
        deviceType: "",
        severitys: "一级,二级,三级",  // 默认报警 一级，二级，三级
        hasFixed: 'N',
        flowStatus: null,
      },

      deviceTypeOptions: [],
      deviceTypesDisplay: [], // 需要显示的种类

      // 选中的设备种类
      deviceType: "",
      // 项目包含的设备种类
      deviceTypes: [],
      // 当前种类下的设备列表
      warningList: [],
      total: 0,

      // 需要报警色设备种类
      deviceWarningTypes: [],
      // 是否已修复字典
      hasFixedOptions: [],
      // 操作状态字典
      flowStatusOptions: [],
      // 设备种类
      deviceTypeOptions: [],
      // 用户列表
      userList: [],

      // 一键全选
      checkedAll: false,
      isIndeterminate: false,
      checkedIdArray: [],
      checkList: [],
      // 排序选择
      orderBy: '',
      // 排序方式
      sortOptions: [
        {
          label: '按最新消息',
          value: 'reportedAt'
        },
        {
          label: '按报警级别',
          value: 'severityLevel'
        }
      ],

      // 循环删除Loading
      deleteLoading: null,

      // 定时刷新
      autoRefresh: null,
      refreshSpan: null,
      refreshSpanOpts: [
        {
          value: null,
          label: "不自动刷新数据",
          selected: true,
        },
        {
          value: 10,
          label: "每10秒刷新数据",
        },
        {
          value: 30,
          label: "每30秒刷新数据",
        },
        {
          value: 60,
          label: "每分钟刷新数据",
        },
        {
          value: 60*5,
          label: "每5分钟刷新数据",
        },
        {
          value: 60*10,
          label: "每10分钟刷新数据",
        },
      ],
    };
  },
  components: {
    WarningCard,
    WarningRepair,
    DeviceWarningDialog,
  },
  computed: {

  },
  created() {
    this.getDicts("d_device_type").then(response => {
      this.deviceTypeOptions = response.data;
      this.deviceTypesDisplayFunc();
    });
    this.getDicts("d_device_warning_has_fixed").then(response => {
      this.hasFixedOptions = response.data;
    });
    this.getDicts("a_item_warning").then(response => {
      this.flowStatusOptions = response.data;
    });
    // 所有用户
    listUser({pageNum:1, pageSize:1000000}).then(response => {
      this.userList = response.rows;
    });
    this.getUser();

    // 自动刷新选项
    if(this.settings.warning_refresh_span) {
      try {
        this.refreshSpanOpts = JSON.parse(this.settings.warning_refresh_span);
      } catch (e) {
        // pass
      }
    }
    console.log(this.refreshSpanOpts, this.refreshSpan);
    if(this.refreshSpan == null) {
      if(this.refreshSpanOpts && this.refreshSpanOpts.length > 0) {
        this.refreshSpanOpts.map( r => {
          if(r.selected) {
            this.refreshSpan = r.value;
          }
        });
      }
    }
  },
  destroyed() {

  },
  mounted() {
    this.getDeviceSummary();
  },
  methods: {
    // 更新自动更新逻辑
    updateAutoRefresh() {
      clearInterval(this.autoRefresh);
      this.autoRefresh = null;
      if(this.refreshSpan) {
        this.autoRefresh = setInterval(() => {
          this.getDeviceSummary();
        }, this.refreshSpan*1000);
      }
    },

    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
      });
    },
    // 切换 tab
    handleDeviceTypeChange() {
      this.getList();
    },
    /** 查询设备维保记录列表 */
    getList() {
      this.loading = true;
      deviceWarningList({
          ...this.queryParams,
          deviceType: this.deviceType == 'all' ? '' : this.deviceType,
          deviceTypes: this.deviceType == 'all' ? '' : this.deviceType,
          hasFixed: this.queryParams.hasFixed,
          orderBy: this.orderBy || ''
        }).then(resp => {
          this.loading = false;
          this.total = resp.total;
          this.warningList = resp.rows.map( r => {
            r.errValue = "";
            let c = r.compare ? r.compare.split(";") : [];
            let lc = c.length > 0 ? c[c.length -1] : "";
            if(lc) {
              let lcl = JSON.parse(lc);
              r.standardValue = lcl[2];
              r.actualValue = lcl[0];
              r.hasFixedText = this.gf.mapStr(r.hasFixed, this.hasFixedOptions);
              r.flowStatusText = this.gf.mapStr(r.flowStatus, this.flowStatusOptions);
            }
            return r;
          });
          this.checkedDefalultData();
        });
    },
    // 筛选设备种类
    deviceTypesDisplayFunc() {
      let list = [];
      let totalDevice = 0;
      let totalWarning = 0;
      if(this.deviceTypeOptions.length > 0 && this.deviceTypes.length > 0) {
        this.deviceTypeOptions.map( dl => {
          this.deviceTypes.map( d => {
            if( d.type == dl.dictValue && dl.remark && dl.remark.indexOf("warning") >= 0) {
              totalDevice += d.totalNum;
              totalWarning += d.warningNum;
              list.push(d);
            }
          });
        })
      }
      list.unshift({
        totalNum: totalDevice,
        type: "all",
        warningNum: totalWarning,
      })
      // console.log(list);

      // 排序
      this.deviceTypes = this.deviceTypes.sort( (a, b) => {
        return a.sort - b.sort;
      });

      // 默认选中第一个
      if(list.length > 0) {
        this.deviceType = list[0].type;
        this.getList();
      }
      this.deviceTypesDisplay = list;
    },

    /** 查询设备列表 */
    getDeviceSummary() {
      deviceSummary({
        buildingId: this.curBuilding.id,
        deviceTypes: this.deviceTypesDisplay.map(d=>d.type).join(","),
        severitys: this.queryParams.severitys,
        hasFixed: this.queryParams.hasFixed,
      })
        .then(({data}) => {
          this.deviceTypes = data;
          this.deviceTypesDisplayFunc();
        });
    },

    handleExport() {
      const queryParams = {
        ...this.queryParams,
        deviceType: this.deviceType == 'all' ? '' : this.deviceType,
        deviceTypes: this.deviceType == 'all' ? '' : this.deviceType,
        hasFixed: this.queryParams.hasFixed,
        orderBy: this.orderBy || ''
      };
      this.$confirm('是否确认导出数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return deviceWarningListExport(queryParams);
        }).then(response => {
          this.download(response.msg);
        })
    },

    deviceTypeName(type) {
      return this.selectDictLabel(this.deviceTypeOptions, type);
    },

    // 刷新数据
    updateData() {
      this.getDeviceSummary();
    },

    // 排序方式选择
    handleSelectChange() {
      this.getDeviceSummary()
    },

    // 点击全选
    checkAllChange(val) {
      this.checkList = val ? this.checkedIdArray : [];
      this.isIndeterminate = false;
    },

    // 获取需要默认显示的数据
    checkedDefalultData() {
        this.checkedIdArray = [];
        this.checkList = [];
          for (var i = 0; i < this.warningList.length; i++) {
            this.checkedIdArray.push(this.warningList[i].id)
        }
    },
    // 选项卡选择
    handleCheckedChange(val) {
      let checkedCount = val.length;
      this.checkedAll = checkedCount === this.warningList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.warningList.length;
    },

    // 删除选中设备
    handleDeleteChecked() {
      this.$confirm('是否确认删除已选择的 ' + this.checkList.length + ' 条设备报警记录?', "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
        }).then(() => {
          this.deleteLoading = this.$loading({
            lock: true,
            text: '数据删除中',
            spinner: 'el-icon-loading',
            customClass: 'loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.deleteDevice(0);
        })
    },
    // 确认选中设备
    handleConfirmChecked(flowStatus) {
      this.$confirm('是否确认已选择的 ' + this.checkList.length + ' 条设备报警记录?', "警告", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
        }).then(() => {
          this.deleteLoading = this.$loading({
            lock: true,
            text: '数据确认中',
            spinner: 'el-icon-loading',
            customClass: 'loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.confirmDevice(0, flowStatus);
        })
    },

    deleteDevice(index) {
      const checkedData = this.checkList;
      deleteDeviceWarning({id: checkedData[index]})
        .then((res) => {
          const indx = index + 1;
          if(indx === checkedData.length) {
            this.deleteLoading.close();
            this.msgSuccess("删除成功");
            checkedData.length == this.checkedIdArray.length && (this.queryParams.pageNum = 1);
            this.getDeviceSummary();
            return false;
          }
          this.deleteDevice(indx)
        })
    },

    confirmDevice(index, flowStatus) {
      const checkedData = this.checkList;
      updateDeviceWarning({id: checkedData[index], flowStatus:flowStatus, operator: this.user.userId })
        .then((res) => {
          const indx = index + 1;
          if(indx === checkedData.length) {
            this.deleteLoading.close();
            this.msgSuccess("操作成功");
            checkedData.length == this.checkedIdArray.length && (this.queryParams.pageNum = 1);
            this.getDeviceSummary();
            return false;
          }
          this.confirmDevice(indx, flowStatus)
        })
    },


    // 删除单个设备
    handleDelete(id) {
      this.$confirm('是否确认删除设备报警记录编号为"' + id + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        return deleteDeviceWarning({id});
      }).then(() => {
        this.msgSuccess("删除成功");
        this.getDeviceSummary();
      })
    },

    // 设备报修
    handleRepair(data) {
      this.$refs.warningRepairDialog.handleRepair(data);
    },


    // 报警操作
    handleUpdate(warning) {
      this.$refs.deviceWarningDialog.handleUpdate({
        ...warning,
        name: warning.deviceName,
      });
    },
  }
};
</script>
<style lang="scss" scoped>
.ribbon-wrap {
  padding: 5px 20px 0;
  font-size: 14px;
  .ribbon-checkbox {
    padding: 5px 15px;
    background: #0A192F;
    border-radius: 4px;
    &::after {
      transition: none!important;
    }
    ::v-deep .el-checkbox__label {
      font-size: 12px;
    }
  }
  ::v-deep .el-select {
    margin-left: 10px;
    margin-right: 10px;
    width: 120px;
    .el-input__inner {
      border-color: #0A192F !important;
      background: #0A192F;
    }
  }
}
.warning-record-container {
  flex-wrap: wrap;
}

</style>
