
// 针对一个图上有多个不同设备的情况
let a = {
  "objects": [
    "1. 静态文字: 纯站位",
    {
      "name": "文字名",   // 自己看，可不填
      "type": "vtext",   // 类型 文字
      "position": {
        "top": "10px",
        "left": "50px"
      },
      "size": "14px",
      "weight": "bold",
      "color": "#fff",
      "data": {
        "dVal": "市电",   // 显示在页面的文字
        "dDataUnit": "(kWh)",  // 显示在页面的文字,   最终会显示为: dVal + 空格 + dDataUnit
      }
    },
    "2. 静态图片: 纯站位",
    {
      "name": "图片名",
      "type": "vdecoration",   // 类型 图片
      "position": {
        "top": "100px",
        "left": "50px"
      },
      "zIndex": 100,
      "iconSize": {
        "width": "80px",
        "height": "80px"
      },
      "url": "https://bms.lanxing.tech/images/face.png",  // 显示在页面的图片
    },
    "3. 静态图文: 纯站位",
    {
      "name": "图片名",
      "type": "vdecoration",   // 类型 图片
      "position": {
        "top": "200px",
        "left": "50px"
      },
      "zIndex": 100,
      "iconSize": {
        "width": "80px",
        "height": "80px"
      },
      "url": "https://bms.lanxing.tech/images/face.png",  // 显示在页面的图片
      "deviceName": "显示的文字",  // 文字会显示在图片下方
      "textStyle": {
        "size": "14px",
        "weight": "bold",
        "color": "#fff",
        "width": "60px",
      }
    },
    "4. 静态图标: 指定设备图标(可点击进详情)-61200",
    {
      "name": "空调001-61200",
      "type": "vdecoration",
      "position": {
        "top": "300px",
        "left": "50px"
      },
      "zIndex": 100,
      "iconSize": {
        "width": "80px",
        "height": "80px"
      },
      "url": "https://bms.lanxing.tech/images/face.png",  // 显示在页面的图片
      "deviceId": 61200,    // 对应设备
      "deviceName": "",     // 空
      "textStyle": {
        "fontSize": "0px",
        "weight": "normal",
        "color": "#fff",
        "width": "80px",
        "text-align": "center",    // 文字居中对齐
      }
    },
    "5. 静态图文: 指定设备图标+文字(可点击进详情)-61200",
    {
      "name": "空调001-61200",
      "type": "vdecoration",
      "position": {
        "top": "400px",
        "left": "50px"
      },
      "zIndex": 100,
      "iconSize": {
        "width": "80px",           // 图片宽度
        "height": "80px"
      },
      "url": "https://bms.lanxing.tech/images/face.png",  // 显示在页面的图片
      "deviceId": 61200,    // 对应设备
      "deviceName": "",     // 自动从数据总加载
      "linkType": "chart",  // 弹框出原型还是详情, chart(原型)/detail(详情)，默认 detail
      "textStyle": {
        "fontSize": "14px",
        "weight": "normal",
        "color": "#fff",
        "width": "80px",           // 文字和图片等宽
        "text-align": "center",    // 文字居中对齐
      }
    },
    "6. 动态图标: 指定设备 (运行，停止不同样式) (可点击进详情)-61201",
    {
      "name": "空调002-61201",
      "type": "vicon",
      "position": {
        "top": "500px",
        "left": "50px"
      },
      "zIndex": 100,
      "iconSize": {
        "width": "80px",
        "height": "80px"
      },
      "url": "https://bms.lanxing.tech/images/face.png",
      "dataId": 6120101,        // 
      "dataName": "运行",       //  显示在页面的图片
      "urls": {
        "0": "/images/IdcsGif/fan/fanLeftFalse.gif",  // 6120101不同值对应的图片
        "1": "/images/IdcsGif/fan/fanLeftTrue.gif",
      }
    },
    "7. 动态文字: 指定数据文字-6120007(61200空调的风速字段 需要翻译)",
    {
      "name": "文字名",   // 自己看，可不填
      "type": "vtext",   // 类型 文字
      "dataId": "6120007",
      "dataName": "风速",
      "position": {
        "top": "10px",
        "left": "200px"
      },
      "size": "14px",
      "weight": "bold",
      "color": "#fff",
    },
    "8. 指定动态数据下拉框-6120007(61200空调的风速字段 需要翻译) 注: relate_item_data_id 需有对应内容",
    {
      "name": "文字名",   // 自己看，可不填
      "type": "vselect",   // 类型 文字
      "dataId": "6120007",
      "dataName": "风速",
      "position": {
        "top": "100px",
        "left": "200px"
      },
    },
    "9. 指定动态数据文字-6120007(61200空调的温度字段 不需要翻译)",
    {
      "name": "文字名",   // 自己看，可不填
      "type": "vtext",   // 类型 文字
      "dataId": "6120005",
      "dataName": "温度",  
      "position": {
        "top": "200px",
        "left": "200px"
      },
      "size": "14px",
      "weight": "bold",
      "color": "#fff",
    },
    "10. 指定动态数据下拉框-6120007(61200空调的温度字段) 注: relate_item_data_id 需有对应内容",
    {
      "name": "文字名",       // 自己看，可不填
      "type": "vinput",      // 类型 文字
      "dataId": "6120005",   // d_device_item_data_map.item_data_id 值
      "dataName": "温度",    // d_device_item_data_map.name 值
      "position": {
        "top": "300px",
        "left": "200px"
      },
    },
  ]
};

// 针对同一类设备，不同ID显示不同内容情况
// 需要先配置菜单
let b = {
  objects: [
    {
      name: "场景填充物",
      type: "vdecoration",
      position: {
        top: "100px",
        left: "150px",
      },
      zIndex: 100,
      iconSize: {
        width: "auto",
        height: "auto",
      },
      url: "/images/IdcsGif/fan/ductB.gif",
    },
    {
      name: "风百页图示",
      type: "vicon",
      dataName: "风速",
      position: {
        top: "350px",
        left: "425px",
      },
      iconSize: {
        width: "auto",
        height: "auto",
      },
      urls: {
        1: "/images/IdcsGif/fan/damperV1.gif",
        2: "/images/IdcsGif/fan/damperV2.gif",
        3: "/images/IdcsGif/fan/damperV3.gif",
        4: "/images/IdcsGif/fan/damperV4.gif",
      }
    },
    {
      name: "风机图示",
      type: "vicon",
      dataName: "运行",
      position: {
        top: "350px",
        left: "515px",
      },
      iconSize: {
        width: "auto",
        height: "auto",
      },
      urls: {
        0: "/images/IdcsGif/fan/fanLeftFalse.gif",
        1: "/images/IdcsGif/fan/fanLeftTrue.gif",
      }
    },
    {
      name: "室内温度Label",
      type: "vtext",
      dataName: "室内温度Label",
      position: {
        top: "290px",
        left: "680px",
      },
      size: "14px",
      weight: "bold",
      color: "#ffffff",
      data: {
        dVal: "室内温度",
        dDataUnit: "",
      }
    },
    {
      name: "室内温度",
      type: "vtext",
      dataName: "室内温度",
      position: {
        top: "315px",
        left: "680px",
      },
      size: "16px",
      weight: "bold",
      color: "#50f100",
    },
    {
      name: "设定温度Label",
      type: "vtext",
      dataName: "设定温度Label",
      position: {
        top: "485px",
        left: "140px",
      },
      size: "14px",
      weight: "bold",
      color: "#ffffff",
      data: {
        dVal: "设定温度",
        dDataUnit: "",
      }
    }, 
    {
      name: "温度更新",
      type: "vinput",
      dataName: "温度",
      position: {
        top: "480px",
        left: "210px",
      },
    },
    {
      name: "运行Label",
      type: "vtext",
      dataName: "运行Label",
      position: {
        top: "485px",
        left: "360px",
      },
      size: "14px",
      weight: "bold",
      color: "#ffffff",
      data: {
        dVal: "运行",
        dDataUnit: "",
      }
    },
    {
      name: "运行更新",
      type: "vselect",
      dataName: "运行",
      position: {
        top: "480px",
        left: "430px",
      },
    }
  ],
}

let c = {
    "objects": [
        "2201-文字",
        {
            "name": "2201-文字",
            "type": "vtext",
            "position": {
                "top": "485px",
                "left": "240px"
            },
            "size": "24px",
            "weight": "bold",
            "color": "#fff",
            "textStyle": {
                "text-shadow": "1px 3px #000",
                "width": "100px",
                "text-align": "center"
            },
            "data": {
                "dVal": "2201",
                "dDataUnit": ""
            },
            "dialogType": "energy",
            "params": {
                "title": "2201 用电数据",
                "deviceIds": "",
                "groupName": "2201"
            }
        },
        "2204-文字",
        {
            "name": "2204-文字",
            "type": "vtext",
            "position": {
                "top": "270px",
                "left": "330px"
            },
            "size": "24px",
            "weight": "bold",
            "color": "#fff",
            "textStyle": {
                "text-shadow": "1px 3px #000",
                "width": "100px",
                "text-align": "center"
            },
            "data": {
                "dVal": "2204",
                "dDataUnit": ""
            },
            "dialogType": "energy",
            "params": {
                "title": "2204 用电数据",
                "deviceIds": "",
                "groupName": "2204"
            }
        },
    ]
}

JSON.stringify(c)



