<template>
    <div>
        <el-dialog
        class="assetsDetail"
        :modal="false"
        width="650px"
        :visible.sync="show"
        v-drag="true" >
        <div slot="title" style="cursor: move;">
            设备信息详情
        </div>
        <div class="box-card">
            <el-form label-width="120px" :model="detail">
            <el-form-item label="设备名称" prop="name">
                <div v-text="detail.name"></div>
            </el-form-item>
            <el-form-item label="供应商信息" prop="company">
                <div v-text="detail.company"></div>
            </el-form-item>
            <el-form-item label="设备编码" prop="description">
                <div v-text="detail.description"></div>
            </el-form-item>
            <el-form-item label="责任人信息" prop="contact">
                <div v-text="detail.contact"></div>
            </el-form-item>
            <el-form-item label="设备品牌" prop="brand">
                <div v-text="detail.brand"></div>
            </el-form-item>
            <el-form-item label="质保时间" prop="activationTime">
                <div v-text="detail.activationTime"></div>
            </el-form-item>
            <el-form-item label="设备型号" prop="model">
                <div v-text="detail.model"></div>
            </el-form-item>
            <el-form-item label="使用年限" prop="serviceLife">
                <div v-text="detail.serviceLife"></div>
            </el-form-item>
            <el-form-item label="安装位置" prop="position">
                <div v-text="detail.position"></div>
            </el-form-item>
            <el-form-item label="原理图" prop="position">
                <el-button type="" size="mini"
              v-if="detail.resourceId>0"
              @click="handleDevicePrototype(detail)">点击查看</el-button>
            </el-form-item>
            </el-form>
        </div>
        </el-dialog>
    </div>
</template>
<script>
import { resourceDevice } from "@/api/device/apis";

export default {
    name: '',
    data() {
        return {
            show: false,
            detail: {},
        }
    },
    computed: {
        
    },
    methods: {
        // 更新显示弹窗
        handleUpdate (resourceId, deviceId) {
            this.show = true;
            this.getDetail(resourceId,deviceId)
        },
          // 跳转原理图
    handleDevicePrototype(device) {
      const { href } = this.$router.resolve({
        path: `/deviceMgt/device/prototype`,
        query: {
          deviceId:device.id,
          deviceName: device.name,
          resourceId:device.resourceId,
          displayType: this.displayType,
        }
      })
      window.open(href, 'prototypePage');
    },
        getDetail (resourceId = '', deviceId = '') {
            const params = {
                    resourceId,
                    deviceId
                }
                resourceDevice(params).then(res => {
                    console.log(res,'res')
                    this.detail = res.data || {}
                })
        }
    }
    
}
</script>
