<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" v-if="formDisplay=='formDisplayIsAvaliabled' || $route.query.lf=='lanxing121' ">
      <h3 class="title">{{project.title}}</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="请输入账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="请输入密码"
          @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-popover
        class="pull-right"
        placement="top-end"
        width="200"
        trigger="click"
        content="请联系管理员重置密码。">
        <span slot="reference" style="color: #2294FE;text-decoration: none;font-size: 14px;cursor: pointer">忘记密码</span>
      </el-popover>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2024 lanxing.tech All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { encrypt, decrypt } from '@/utils/jsencrypt';
import { getCodeImg, settings } from "@/api/login";
import {getUserProfile, listUser} from "@/api/system/user";
import { autoLogin } from "@/api/base/apis";
import Cookies from "js-cookie";
import store from '@/store'
import {
  getToken, setToken, removeToken,
  getPubKey, setPubKey, removePubKey, setPubKey2,
} from '@/utils/auth';

export default {
  name: "Login",
  data() {
    return {
      // 载入项目配置
      // projectSettings: this.gf.projectSettings(),
      formDisplay: false,
      formDisplaySettings:false, // db中配置的显示
      project: this.gf.projectInfo(),
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" }
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
          { validator: this.gf.validatePass, trigger: "blur" },
        ],
        code: [{ required: true, trigger: "change", message: "验证码不能为空" }]
      },
      loading: false,
      redirect: undefined,

      checkLogin: null,  // 指纹自动登录检查

      // 自动登录
      // 只有输入正确路由，才能自动登录
      urlAutoLogin: this.$route.query.autoLogin || "",  // 有这个参数会主动验证登录
      urlUsername: this.$route.query.username || "",
      urlPassword: this.$route.query.password || "",

      // 支持任意路由都能自动登录
      autoLogin: 0,  // 默认不自动登录
      loginUsername: "",
      loginPassword: "",

    };
  },
  watch: {
    // $route: {
    //   handler: function(route) {
    //     // this.redirect = route.query && route.query.redirect;
    //   },
    //   immediate: true
    // }
  },
  created() {
    this.updatePublicKey();
    // 加载配置
    this.getDicts("base_configs")
      .then(response => {
        if(response && response.data && response.data.length > 0) {
          response.data.filter(d => {
            if(d.dictLabel == "ssologin") {
              this.ssologin = d.dictValue;
            } else if(d.dictLabel == "ssoRedirect") {
              this.ssoRedirect = d.dictValue;
            } else if(d.dictLabel == "loginRedirect") {
              this.redirect = d.dictValue;
            } else if(d.dictLabel == "formDisplaySettings") {
              this.formDisplaySettings = d.dictValue;
            } else if(d.dictLabel == "autoLogin") {
              this.autoLogin = d.dictValue;
            } else if(d.dictLabel == "loginUsername") {
              this.loginUsername = d.dictValue;
            } else if(d.dictLabel == "loginPassword") {
              this.loginPassword = d.dictValue;
            }
          });
        }
      }).then(() => {
        this.redirectLoginPage();
      }).then(() => {
        this.doAutoLogin();
      });
  },
  destroyed() {
    clearInterval(this.checkLogin);
    this.checkLogin = null;
  },
  methods: {
    redirectLoginPage() {
      if(this.ssologin && this.ssologin == "Y" && this.ssoRedirect && this.ssoRedirect != "") {
        // this.$router.replace({ path: this.ssoRedirect })
        // 通过 url 增加参数阻止跳转
        if(this.$route.query.lf != "lanxing121") {
          window.location.href = this.ssoRedirect;
        } else {
          this.formDisplay = this.formDisplaySettings;
        }
      } else {
        this.formDisplay = this.formDisplaySettings;
      }
      console.log(this.formDisplay);
    },
    updatePublicKey() {
      store.dispatch('getPublicKey')
        .then(res => {
          // let publicKey = res.publicKey;
          // setPubKey(publicKey);
          // store.commit('SET_PUBLICKEY', publicKey);
          // let publicKey2 = res.sPublicKey;
          // setPubKey2(publicKey2);
          // store.commit('SET_PUBLICKEY2', publicKey2);
        }).then(() => {
          this.getCode();
          this.getCookie();

          // 判断能否自动登录
          settings()
            .then( ({data}) => {
              console.log(data);
              if(data.autoLoginCheck == 1) {
                this.checkAutoLogin();
              }
            });
        });
    },

    doAutoLogin() {
      if(this.autoLogin == 1) {
        // 完全自动登录
        this.handleAutoLogin(this.loginUsername, this.loginPassword);
      } else if(this.urlAutoLogin == 1) {
        // 根据路由参数执行主动登录
        this.handleAutoLogin(this.urlUsername, this.urlPassword);
      }
    },

    handleAutoLogin(loginUsername, loginPassword) {
      this.loginForm = {
        username: loginUsername,
        password: loginPassword,
        rememberMe: false,
        code: "123",
        uuid: "123"
      };
      this.$store.dispatch("ApiLogin", this.loginForm)
        .then(() => {
          return getUserProfile();
        }).then(() => {
          return listUser();
        }).then(() => {
          this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
          console.log('autoLogin................finish')
          window.parent.postMessage(
            {
              type: "autoLogin",
              status: 1,//响应状态
              message: {
                name: 'finish',
              }
            },
            "*"
          );
        }).catch(() => {
          this.loading = false;
          this.getCode();
          this.updatePublicKey();
          store.dispatch('FedLogOut');
        });
    },

    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },

    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 3000 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 3000 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 3000 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm)
            .then(() => {
              return getUserProfile();
            }).then(() => {
              return listUser();
            }).then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
            }).catch(() => {
              this.loading = false;
              this.getCode();
              this.updatePublicKey();
              store.dispatch('FedLogOut');
            });
        }
      });
    },

    handleApiLogin() {
      this.$store.dispatch("ApiLogin", this.loginForm)
        .then(() => {
          return getUserProfile();
        }).then(() => {
          return listUser();
        }).then(() => {
          this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
        }).catch(() => {
          this.loading = false;
          this.getCode();
          this.updatePublicKey();
          store.dispatch('FedLogOut');
        });
    },

    // 1秒一次轮询，检查能否自动登录 for 指纹登录
    checkAutoLogin() {
      let that = this;
      this.checkLogin = setInterval(() => {
        clearInterval(that.checkLogin);
        that.checkLogin = null;
        autoLogin().then(res => {
          if(res.data && res.data.logined == 1) {
            that.loginForm = {
              username: res.data.user,
              password: res.data.pwd,
              rememberMe: false,
              code: "123",
              uuid: "123"
            };
            that.handleApiLogin();
          } else {
            that.checkAutoLogin();
          }
        }).catch(e => {
          that.checkAutoLogin();
        });
      }, 1000);
    },

    redirectHomePage() {
      if(this.redirect && this.redirect != "") {
        this.$router.replace({ path: '/' + this.redirect })
        // #/energyMgt/summary/energy
      } else {
        this.loading = false;
      }
    },
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #000205;
  background-image: url("/images/login-bg.png");
  background-size: 100% 100%;
}
.login .title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #fff;
}

.login-form {
  border-radius: 6px;
  width: 500px;
  padding: 30px 75px 10px;
  border: 1.2px solid rgba(31, 71, 119, 1);
  background: rgba(7, 26, 58, 0.2);
  box-sizing: border-box;
  box-shadow: 0px 0px 34.8px 9.3px rgba(26, 59, 118, 1) inset;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
