<template>
  <div>
    <el-dialog
      class="assetsDetail"
      :modal="false"
      width="650px"
      :visible.sync="show"
      v-drag="true"
    >
      <div slot="title" style="cursor: move">工单详情</div>
      <div class="box-card">
        <Steps :status="detail.status"></Steps>
        <el-form label-width="120px" :model="detail">
          <el-form-item label="工单号" prop="id">
            <div v-text="detail.id"></div>
          </el-form-item>
          <el-form-item label="设备" prop="deviceName">
            <div v-text="detail.deviceName"></div>
          </el-form-item>
          <el-form-item label="维保分类" prop="type">
            <div v-text="detail.type"></div>
          </el-form-item>
          <el-form-item label="执行状态" prop="status">
            <div v-text="detail.statusName"></div>
          </el-form-item>
          <el-form-item label="执行记录" prop="note">
            <div v-text="detail.note"></div>
          </el-form-item>
          <el-form-item label="报警信息" prop="record">
            <div v-text="detail.record"></div>
          </el-form-item>
          <el-form-item label="备注信息" prop="note">
            <div v-text="detail.note"></div>
          </el-form-item>
          <el-form-item label="操作人" prop="operator">
            <div v-text="detail.operator"></div>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <div v-text="detail.mobile"></div>
          </el-form-item>
          <el-form-item label="创建时间" prop="createdAt">
            <div v-text="detail.createdAt"></div>
          </el-form-item>
          <el-form-item label="结束时间" prop="finishedAt">
            <div v-text="detail.finishedAt"></div>
          </el-form-item>
          <el-form-item label="更新时间" prop="updatedAt">
            <div v-text="detail.updatedAt"></div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getMaintenance } from "@/api/device/maintenance";
import Steps from "./components/steps.vue";
import { getStatusName } from './common/index'
export default {
  name: "",
  components: {
    Steps,
  },
  props: {
    deviceList: {
        type: Array,
        default () {
            return []
        }
    }
  },
  data() {
    return {
      show: false,
      detail: {},
    };
  },
  computed: {},
  methods: {
    // 更新显示弹窗
    handleUpdate(id) {
      this.show = true;
      this.getDetail(id);
    },
    getDetail(id = "") {
      getMaintenance(id).then((res) => {
        console.log(res);
        this.detail = res.data;
        const statusName = getStatusName(this.detail.status);
        this.detail.statusName = statusName;
        this.detail.deviceName = (this.deviceList.find(item => item.id == this.detail.deviceId) || {}).name
      });
    },
  },
};
</script>
