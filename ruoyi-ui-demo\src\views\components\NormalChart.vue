<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
//require('echarts/theme/macarons') // echarts theme
require("echarts/theme/dark"); // echarts theme
import resize from '@/utils/resize';

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    opts: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null,
      defatulOpts: {
        darkMode: true,
        backgroundColor: 'transparent',
        color: this.gf.chartColors(),
        xAxis: {
          // data: [],
          // boundaryGap: false,
          // axisTick: {
          //   show: false
          // }
        },
        grid: {
          left: 10,
          right: 80,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          // axisTick: {
          //   show: false
          // }
        },
        legend: {
          data: []
        },
        series: []
      },

      class2type: {},
    }
  },
  watch: {
    opts: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = this.$echarts.init(this.$el, 'dark');
      this.setOptions(this.opts, true);
    },
    setOptions(opts, isInit = false) {
      var o = this.gf.extend(true, this.defatulOpts, this.opts);
      console.log("NormalChart -----> ", o);
      if(this.chart) {
        // 只在初始化时清空图表，数据更新时不清空避免闪烁
        if (isInit) {
          this.chart.clear();
        }
        this.$nextTick( () => {
          // 使用 notMerge: false 来合并更新，避免重新渲染整个图表
          this.chart.setOption(o, !isInit);
          this.chart.resize();
        });
      }
    },
  }
}

</script>
