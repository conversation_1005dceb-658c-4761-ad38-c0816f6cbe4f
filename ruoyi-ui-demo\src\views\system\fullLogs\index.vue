<template>
  <el-container>
    
    <el-main class="xcContainer" >
      <div class="main-card p15">
      <h3>
        系统日志
        <div class="clearfix"></div>
        <el-divider></el-divider>
      </h3>

      <el-tabs v-model="activeName" lazy="true" @tab-click="handleTabClick">
        <el-tab-pane label="登录日志" name="loginlog">
          <Logininfor v-if="activeName=='loginlog'" />
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="operlog">
          <Operlog v-if="activeName=='operlog'" />
        </el-tab-pane>
        <el-tab-pane label="设备日志" name="devicelog">
          <Devicelog v-if="activeName=='devicelog'" />
        </el-tab-pane>
      </el-tabs>
      </div>
    </el-main>
    
  </el-container>
</template>

<script>
import Operlog from "./operlog";
import Logininfor from "./loginlog";
import Devicelog from "./devicelog";

export default {
  name: "系统日志",
  components: {
    Operlog,
    Logininfor,
    Devicelog,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 建筑ID
      buildingId: this.gf.getBuildingId(),

      activeName: "loginlog",

    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleTabClick() {
      
    }
  }
}
</script>
