<template>
  <div class="shift_summary">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="120px">
      <el-form-item label="值班人" prop="userId">
        <el-select v-model="queryParams.userId" placeholder="请选择值班人" clearable size="small" :multiple="false">
          <el-option v-for="dict in userList" :key="dict.userId" :label="dict.nickName" :value="dict.userId" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="date">
        <el-date-picker clearable size="small" style="width: 200px" v-model="queryParams.date" type="month"
          value-format="yyyy-MM" placeholder="请选择年月">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-table v-loading="loading" :data="tableData">
      <el-table-column label="值班人" align="center" prop="userId" :formatter="userIdFormat" />
      <el-table-column label="班次类型" align="center" prop="shiftType" :formatter="shiftTypeFormat" />
      <el-table-column label="当班开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当班结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注信息" align="center" prop="note" />
    </el-table>
    <!-- 排班表 -->
    <ScheduleView :shiftTypeOptions="shiftTypeOptions" :date="scheduleViewProps.date" :userList="userList" :shiftList="tableData" :userId="scheduleViewProps.userId"></ScheduleView>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";
import { listPiShiftSchedule } from "@/api/inspection/shiftSchedule.js";
import ScheduleView from "./scheduleView.vue";
export default {
  data() {
    return {
      queryParams: {
        date: '2024-08'
      },
      userList: [],
      tableData: [],
      total: 0,
      loading: false,
      // 班次类型字典
      shiftTypeOptions: [],
      scheduleViewProps: {}
    };
  },
  components: {
    ScheduleView
  },
  created() {
    this.getUserList();
    this.getDicts("shift_schedule_type").then(response => {
      this.shiftTypeOptions = response.data;
    });
    this.getList();
  },
  mounted () {
    this.handleQuery()
  },
  methods: {
    // 所有用户
    getUserList() {
      listUser({ pageNum: 1, pageSize: 1000000 }).then(response => {
        this.userList = response.rows;
      });
    },
    /** 搜索按钮操作 */
    async handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 1000000;
      await this.getList();
      this.scheduleViewProps = {...this.queryParams}
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getList() {
      this.loading = true;
      const queryParams = {
        ...this.queryParams,
        start_time: this.queryParams.date + '-01 00:00:00',
        end_time: this.queryParams.date + '-31 23:59:59',
      }
      delete queryParams.date;
      listPiShiftSchedule(queryParams).then(response => {
        this.tableData = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 值班人字典翻译
    userIdFormat(row, column) {
      const it = this.userList.find(item => item.userId === row.userId);
      return it.nickName;
    },
    // 班次类型字典翻译
    shiftTypeFormat(row, column) {
      return this.selectDictLabel(this.shiftTypeOptions, row.shiftType);
    },
  }
};
</script>

<style scoped lang="scss">
.shift_summary {
  padding: 16px;
}
</style>