<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="日志标题" prop="title" class="pr15">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入日志标题"
          clearable
          style="width: 200px;"
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="操作人员" prop="operName">
        <el-input
          v-model="queryParams.operName"
          placeholder="请输入操作人员"
          clearable
          style="width: 200px;"
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="设备类型" prop="deviceTypeList">
        <el-select
          v-model="queryParams.deviceTypeList"
          placeholder="设备类型"
          clearable
          size="small"
          style="width: 160px"
          multiple
          collapse-tags
        >
          <el-option
            v-for="dict in deviceTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="状态" prop="status" class="pr15">
        <el-select
          v-model="queryParams.status"
          placeholder="操作状态"
          clearable
          size="small"
          style="width: 120px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="操作时间" class="pr15">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"

        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="small" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['monitor:operlog:remove']"
          v-hasRole="['admin','operator']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['monitor:operlog:remove']"
          v-hasRole="['admin']"
        >清空</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['monitor:operlog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" id="deviceLog" @selection-change="handleSelectionChange" stripe>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志编号" align="center" prop="id" width="80" />
      <el-table-column label="设备类型" align="center" prop="deviceType" :formatter="deviceTypeFormat" />
      <el-table-column label="设备名称" align="操作内容" prop="deviceName">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.jsonResultObj" class="item" effect="dark"
            :content="scope.row.jsonResultObj.deviceId" placement="top">
            <div v-text="scope.row.jsonResultObj.deviceName" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="left" prop="title" >
        <template slot-scope="scope">
          <div>
            <div v-if="scope.row.jsonResultObj && scope.row.jsonResultObj['dataName']"
              v-text="scope.row.jsonResultObj['dataName']" />
            <div v-else v-text="scope.row.title" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作内容" align="left" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="scope.row.jsonResultObj">
            <span v-text="scope.row.jsonResultObj['dataValueBeforeStr'] || scope.row.jsonResultObj['dataValueBefore']"></span>
            ->
            <span v-text="scope.row.jsonResultObj['dataValueAfterStr'] || scope.row.jsonResultObj['dataValueAfter']"></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作状态" align="center" prop="status" :formatter="statusFormat" />
      <!-- <el-table-column label="操作类型" align="center" prop="businessType" :formatter="typeFormat" /> -->
      <el-table-column label="执行方式" align="center" prop="method" :formatter="methodFormat" />
      <!-- <el-table-column label="请求方式" align="center" prop="requestMethod" /> -->
      <el-table-column label="操作人员" align="center" prop="operName" />
<!--       <el-table-column label="主机" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
      <el-table-column label="操作地点" align="center" prop="operLocation" :show-overflow-tooltip="true" /> -->
      <el-table-column label="操作日期" align="center" prop="operTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.operTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
            v-hasPermi="['monitor:operlog:query']"
          >详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      :background="false"
    />

    <!-- 操作日志详细 -->
    <el-dialog title="操作日志详细" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作模块：">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>
            <el-form-item
              label="登录信息："
            >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作状态：">
              <div v-if="form.status === 0">正常</div>
              <div v-else-if="form.status === 1">失败</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作时间：">{{ parseTime(form.operTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deviceOperateLogList2, deleteDeviceOperateLog, cleanDeviceOperatelog } from "@/api/device/apis";

export default {
  name: "Devicelog",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      deviceTypeOptions: [],
      // 类型数据字典
      typeOptions: [],
      // 类型数据字典
      statusOptions: [],
      // 日期范围
      dateRange: [new Date(), new Date()],
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        operName: undefined,
        businessType: undefined,
        status: undefined
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_oper_type").then(response => {
      this.typeOptions = response.data;
    });
    this.getDicts("sys_common_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("d_device_type").then(response => {
      this.deviceTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询登录日志 */
    getList() {
      this.loading = true;
      this.queryParams.deviceTypes = this.queryParams.deviceTypeList ? this.queryParams.deviceTypeList.join(',') : '';
      deviceOperateLogList2(this._addDateRange(this.queryParams, this.dateRange)).then( response => {
          this.list = response.rows.map( d => {
            try {
              d.jsonResultObj = JSON.parse(d.jsonResult);
              if(d.jsonResultObj.hasOwnProperty("dataValueMapper") && d.jsonResultObj.dataValueMapper) {
                try {
                  d.jsonResultObj.dataValueMapper = JSON.parse(d.jsonResultObj.dataValueMapper);
                  d.jsonResultObj.dataValueBeforeStr = d.jsonResultObj.dataValueMapper[d.jsonResultObj.dataValueBefore];
                  d.jsonResultObj.dataValueAfterStr = d.jsonResultObj.dataValueMapper[d.jsonResultObj.dataValueAfter];
                } catch(e) {
                  // pass
                }
              }
            } catch(e) {
              d.jsonResultObj = null;
            }
            return d;
          });
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    _addDateRange(params, dateRange) {
      console.log(params,dateRange, 'dateRange')
      var search = params;
      search.params = {};
      if (null != dateRange && '' != dateRange) {
        if (typeof (propName) === "undefined") {
          search["beginTime"] = this.$moment(dateRange[0]).format('YYYY-MM-DD');
          search["endTime"] = this.$moment(dateRange[1]).format('YYYY-MM-DD');
        } else {
          search["begin" + propName] = this.$moment(dateRange[0]).format('YYYY-MM-DD');
          search["end" + propName] = this.$moment(dateRange[1]).format('YYYY-MM-DD');
        }
      } else {
        search["beginTime"] = '';
        search["endTime"] = '';
      }
      return search;
    },
    // 操作日志状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    deviceTypeFormat(row, column) {
      return this.selectDictLabel(this.deviceTypeOptions, row.deviceType);
    },
    // 操作日志类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.businessType);
    },
    methodFormat(row, column) {
      return row.method.indexOf("Tasks") >= 0 ? "任务变更" : "数据变更";
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除日志编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return deleteDeviceOperateLog({
            ids: ids.join(",")
          });
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 清空按钮操作 */
    handleClean() {
        this.$confirm('是否确认清空所有操作日志数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return cleanDeviceDevicelog();
        }).then(() => {
          this.getList();
          this.msgSuccess("清空成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出设备操作日志数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          that.et('deviceLog', "设备操作日志数据");
          // return exportDevicelog(queryParams);
        }).then(response => {
          // this.download(response.msg);
        })
    },

  }
};
</script>

