<template>
  <div class="app-container new-system-container">
    <div class="main-card">
      <h3 class="mb10">
        报修记录
        <el-divider></el-divider>
      </h3>
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="120px">
        <el-form-item label="" prop="status">
          <el-select v-model="queryParams.status" clearable size="small" style="width: 240px">
            <el-option :label="item.title" :value="item.status" v-for="(item,index) in statusMap" :key="index"/>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="searchParam">
          <el-input v-model="queryParams.searchParam" size="small" placeholder="快速查找" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="getList">搜索</el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div class="tableList">
        <el-table v-loading="loading" stripe
            :data="recordsList"
            :row-class-name="tableRowClassName">
          <el-table-column label="ID" align="center" prop="id" />
          <el-table-column label="报修单号" align="center" prop="orderId" />
          <el-table-column label="报修类型" align="center" prop="repairTypeName" />
          <el-table-column label="报修状态" align="center" prop="model" width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 'new'">待派单</span>
              <span v-if="scope.row.status == 'dispatched'">已派单</span>
              <span v-if="scope.row.status == 'received'">已接单</span>
              <span v-if="scope.row.status == 'doing'">处理中</span>
              <span v-if="scope.row.status == 'finished'">已完成</span>
            </template>
          </el-table-column>
          <el-table-column label="问题描述" align="center" prop="content" />
          <el-table-column label="报修人" align="center" prop="applyBy" />
          <el-table-column label="联系电话" align="center" prop="mobile" />
          <el-table-column label="故障地址" align="center" prop="address" />
          <el-table-column label="创建时间" align="center" prop="createdAt" />
          <el-table-column label="更新时间" align="center" prop="updatedAt" />
          <el-table-column label="操作" align="center" width="300" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleView(scope.row)"
              >详情</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleEdit(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >删除</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-promotion"
                v-if="!scope.row.maintenanceId"
                @click="handleDispatch(scope.row)"
              >派单</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                v-else
                @click="handleViewOrderDetail(scope.row)"
              >查看工单</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 弹框 编辑设备详情  -->
    <el-dialog
      class="assetsDetail"
      :modal="false"
      width="650px"
      :visible.sync="showEditDialog"
      v-drag="true" >
      <div slot="title" style="cursor: move;">
        编辑报修单
      </div>
      <div class="box-card" @mousedown="mousedownDisabled" @mousemove="mousemoveDisabled" >
        <el-form ref="editForm" label-width="120px" :model="editDetail" :rules="rules">
          <el-form-item label="报修单编号" prop="orderId">
            <div v-text="editDetail.orderId"></div>
          </el-form-item>
          <el-form-item label="报修类型" prop="repairType">
            <el-select v-model="editDetail.repairType" placeholder="请选择">
              <el-option
                v-for="item in repairTypeMap"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备ID" prop="deviceId">
            <el-input v-model="editDetail.deviceId"></el-input>
          </el-form-item>
          <el-form-item label="报修状态" prop="status">
            <el-select v-model="editDetail.status" placeholder="请选择">
              <el-option
                v-for="item in statusMap"
                :key="item.value"
                :label="item.title"
                :value="item.status">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="问题描述" prop="content">
            <el-input v-model="editDetail.content"></el-input>
          </el-form-item>
          <el-form-item label="报修人" prop="applyBy">
            <el-input v-model="editDetail.applyBy"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="editDetail.mobile"></el-input>
          </el-form-item>
          <el-form-item label="报修人公司" prop="company">
            <el-input v-model="editDetail.company"></el-input>
          </el-form-item>
          <el-form-item label="故障地址" prop="address">
            <el-input v-model="editDetail.address"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="showEditDialog = false">取 消</el-button>
        </div>
    </el-dialog>
    <RepairOrderDetailDialog ref="repairOrderDetailDialogRef"></RepairOrderDetailDialog>
    <OrderDetailDialog ref="orderDetailDialogRef"></OrderDetailDialog>
  </div>
</template>

<script>
import DisplayBaseFunc from "@/views/device/monitor/components/Device/DisplayBaseFunc"
import RepairOrderDetailDialog from './components/repairOrderDetailDialog'
import OrderDetailDialog from "./detail";
import {
  repairOrderList,
  repairOrderUpdate,
  repairOrderDelete
} from "@/api/device/apis";
import { getStatusName, statusMap } from './common/index'
export default {
  name: "RepairRecords",
  components: {
  },
  mixins: [DisplayBaseFunc],
  components: {
    RepairOrderDetailDialog,
    OrderDetailDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      curBuilding: this.gf.getCurBuilding(),
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        buildingId: this.gf.getBuildingId(),
        userId: null,
        status: null,
        searchParam: "",
      },
      recordsList: [],
      total: 0,
      editDetail: {},
      showEditDialog: false,
      repairTypeMap: [
        {
          value: 'electrical',
          label: "电气维修"
        },{
          value: 'waterPipes',
          label: "水管维修"
        },{
          value: 'metope',
          label: "墙面维修"
        },{
          value: 'other',
          label: "其他维修"
        },
      ],
      rules: {
        applyBy: [{ required: true, message: '请输入报修人姓名', trigger: 'blur' }],
        mobile: [{
            required: true,
            message: '请输入报修人联系电话', trigger: 'blur'
          },
          {
            pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/,
            message: '联系电话输入格式不正确', trigger: 'blur'
          }],
        company: [{ required: true, message: '请输入报修人公司', trigger: 'blur' }],
        repairType: [{ required: true, message: '请选择报修类型', trigger: 'change' }],
        status: [{ required: true, message: '请选择报修状态', trigger: 'change' }],
        content: [{ required: true, message: '请输入故障描述', trigger: 'change' }],
        deviceId: [{ required: true, message: '请输入设备ID', trigger: 'blur'},{pattern: /^[0-9]*$/, message: '设备ID只能输入数字', trigger: 'change' }],
        priority: [{ required: true, message: '请选择期望程度', trigger: 'change' }],
        address: [{ required: true, message: '请输入故障地址', trigger: 'blur' }]
      },
      statusMap
    };
  },
  computed: {
  },
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        buildingId: this.gf.getBuildingId(),
        userId: null,
        status: null,
        searchParam: "",
      };
      this.getList();
    },

    getList() {
      this.loading = true;
      repairOrderList(this.queryParams).then(({rows = [], total}) => {
        rows.forEach(item => {
          item.repairTypeName = (this.repairTypeMap.find(it => it.value === item.repairType) || {}).label;
        })
        this.recordsList = [...rows]
        this.total = total;
        this.loading = false;
      });
    },
    tableRowClassName({row, rowIndex}) {
      let s = parseInt(row.status);
      if (s == "finished") {
        return 'default-row';
      }
      return '';
    },
    // 查看详情
    handleView({id = ''}) {
      this.$refs.repairOrderDetailDialogRef.handleUpdate(id)
    },
    // 编辑详情
    handleEdit(row) {
      this.editDetail = JSON.parse(JSON.stringify(row));
      this.showEditDialog = true
    },
    // 删除
    handleDelete(row) {
      const id = row.id;
      this.msgSuccess("删除成功");
      const that = this;
      this.$confirm('是否确认删除当前报修单？', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          repairOrderDelete(id).then(res => {
            that.getList();
            that.msgSuccess("删除成功");
          })
        })
    },
    submitForm () {
      this.$refs['editForm'].validate((valid) => {
        if(valid) {
          repairOrderUpdate(this.editDetail).then(res => {
            console.log(res)
            this.showEditDialog = false;
            this.getList();
          })
        }
      })
    },
    handleDispatch ({id = '',deviceId = ''}) {
      this.$router.push({
        path:'/maintananceMgt/repair',
        query:{
          deviceId,
          repairOrderId: id,
          method:"add"
        }
      });
    },
    handleViewOrderDetail (data) {
      const {maintenanceId = ''} = data
      this.$refs.orderDetailDialogRef.handleUpdate(maintenanceId)
    }
  },
};
</script>
<style lang="scss" scoped>

</style>
